# -*- coding: utf-8 -*-
"""
煙霧偵測模型訓練與影像自動分類 GUI

使用 ttkbootstrap 建立一個現代化的使用者介面，用於執行模型訓練和影像分類任務。
所有耗時操作都在背景執行緒中運行，以保持介面流暢。
"""
import tkinter as tk
from tkinter import scrolledtext
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

import cv2
import os
import sys
import numpy as np
import json
import logging
import traceback
import configparser
import shutil
import threading
import queue

from skimage.feature import hog, local_binary_pattern
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report

# --- 核心處理邏輯 (從上一個腳本複製，保持不變) ---
# ... (此處省略了與上一個答案中完全相同的函式，以節省空間) ...
# 實際使用時，請將 `main_classify_script.py` 中除了 `main()` 和 `if __name__ == "__main__"`
# 之外的所有函式複製到這裡。
# 為了完整性，我將它們包含在下面，但折疊起來。

#<editor-fold desc="核心處理邏輯函式">
def load_config(config_file='config.ini'):
    """載入設定檔"""
    config = configparser.ConfigParser()
    if not os.path.exists(config_file):
        logging.error(f"設定檔未找到: {config_file}")
        return None
    try:
        config.read(config_file, encoding='utf-8')
        logging.info(f"設定已從 {config_file} 載入")
        return config
    except configparser.Error as e:
        logging.error(f"解析設定檔 {config_file} 錯誤: {e}", exc_info=True)
        return None

def load_roi_from_config(config_path):
    """從 JSON 檔案載入 ROI 區域"""
    if not os.path.exists(config_path):
        logging.error(f"ROI 設定檔未找到: {config_path}")
        return []
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        rois = [roi_dict.get("vertices") for roi_dict in data.get("rois", []) if roi_dict.get("vertices")]
        logging.info(f"已從 {config_path} 載入 {len(rois)} 個 ROI")
        return rois
    except Exception as e:
        logging.error(f"從 {config_path} 載入 ROI 設定錯誤: {e}", exc_info=True)
        return []

def extract_features(image, roi_vertices=None, feature_config=None, fixed_size=(128,128)):
    """對單一影像或其ROI區域萃取特徵"""
    try:
        if image is None or feature_config is None: return None
        use_hog = feature_config.getboolean('use_hog', False)
        hog_ppc = tuple(map(int, feature_config.get('hog_params', '16,16,2,2').split(',')[:2]))
        hog_cpb = tuple(map(int, feature_config.get('hog_params', '16,16,2,2').split(',')[2:]))
        use_lbp = feature_config.getboolean('use_lbp', False)
        lbp_params = feature_config.get('lbp_params', '8,1,uniform').split(',')
        lbp_p, lbp_r = int(lbp_params[0]), int(lbp_params[1])
        lbp_method = lbp_params[2]

        if roi_vertices is not None:
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            roi_corners = np.array([roi_vertices], dtype=np.int32)
            cv2.fillPoly(mask, roi_corners, 255)
            x, y, w, h = cv2.boundingRect(roi_corners)
            image_processed = cv2.bitwise_and(image, image, mask=mask)[y:y+h, x:x+w]
        else:
            image_processed = image

        if image_processed.size == 0: return None
        image_processed = cv2.resize(image_processed, fixed_size)
        gray = cv2.cvtColor(image_processed, cv2.COLOR_BGR2GRAY)

        features = []
        if use_hog:
            hog_features = hog(gray, pixels_per_cell=hog_ppc, cells_per_block=hog_cpb, visualize=False)
            features.append(hog_features)
        if use_lbp:
            lbp = local_binary_pattern(gray, P=lbp_p, R=lbp_r, method=lbp_method)
            n_bins = int(lbp.max() + 1)
            (hist, _) = np.histogram(lbp.ravel(), bins=n_bins, range=(0, n_bins))
            hist = hist.astype("float")
            hist /= (hist.sum() + 1e-6)
            features.append(hist)

        return np.concatenate(features) if features else None
    except Exception:
        # 減少日誌噪音，因為這可能在正常操作中發生
        # logging.error(f"特徵萃取錯誤: {e}", exc_info=True)
        return None

def load_dataset(dataset_dir, rois, feature_config, fixed_size):
    """從資料夾載入影像並萃取特徵，建立資料集"""
    data, labels = [], []
    logging.info(f"正在從 {dataset_dir} 載入資料集")
    if not os.path.exists(dataset_dir):
        logging.error(f"資料集目錄未找到: {dataset_dir}")
        return np.array(data), np.array(labels)

    for label_name in ['smoke', 'no_smoke']:
        dir_path = os.path.join(dataset_dir, label_name)
        if not os.path.exists(dir_path):
            logging.warning(f"目錄 {dir_path} 不存在，跳過。")
            continue

        label = 1 if label_name == "smoke" else 0
        image_files = [f for f in os.listdir(dir_path) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        logging.info(f"在 {dir_path} 中找到 {len(image_files)} 張影像。")
        for filename in image_files:
            file_path = os.path.join(dir_path, filename)
            image = cv2.imread(file_path)
            if image is None:
                logging.warning(f"無法讀取影像: {file_path}")
                continue

            all_roi_features = []
            if rois:
                for roi in rois:
                    feats = extract_features(image, roi_vertices=roi, feature_config=feature_config, fixed_size=fixed_size)
                    if feats is not None:
                        all_roi_features.append(feats)
            else:
                feats = extract_features(image, roi_vertices=None, feature_config=feature_config, fixed_size=fixed_size)
                if feats is not None:
                    all_roi_features.append(feats)

            if all_roi_features:
                final_features = np.mean(all_roi_features, axis=0)
                data.append(final_features)
                labels.append(label)
            else:
                logging.warning(f"無法從影像 {file_path} 的任何ROI中萃取特徵。")

    logging.info(f"資料集載入完成。總樣本數: {len(data)}")
    return np.array(data), np.array(labels)

def train_classifier(X, y, classifier_config):
    """訓練SVM分類器"""
    if len(X) < 10:
        logging.warning(f"樣本數 ({len(X)}) 過少，不進行測試集分割，直接使用所有資料訓練。")
        X_train, y_train = X, y
        X_test, y_test = None, None
    else:
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    svm_kernel = classifier_config.get('svm_kernel', 'linear')
    logging.info(f"正在訓練 SVC，核函數='{svm_kernel}'")

    clf = SVC(kernel=svm_kernel, probability=True, random_state=42)
    clf.fit(X_train, y_train)

    if X_test is not None:
        y_pred = clf.predict(X_test)
        report = classification_report(y_test, y_pred, target_names=['no_smoke', 'smoke'], zero_division=0)
        logging.info("模型評估報告:\n" + report)
    
    if X_test is not None: # 如果有分割測試集，用全部資料再訓練一次
        logging.info("正在使用全部資料重新訓練最終模型...")
        final_clf = SVC(kernel=svm_kernel, probability=True, random_state=42)
        final_clf.fit(X, y)
        return final_clf
    else: # 否則直接回傳已訓練好的模型
        return clf

def train_model_for_side(side, config, rois):
    """為指定側(left/right)訓練一個模型"""
    logging.info(f"--- 開始為 [{side.upper()}] 側訓練模型 ---")
    train_dir = os.path.join(side, 'train')
    
    if not rois:
        logging.error(f"[{side.upper()}] 側未定義ROI，無法進行訓練。")
        return None

    feature_config = config['Features']
    classifier_config = config['Classifier']
    fixed_size = (config.getint('Detection', 'fixed_image_width', fallback=128),
                  config.getint('Detection', 'fixed_image_height', fallback=128))

    X, y = load_dataset(train_dir, rois, feature_config, fixed_size)
    if X.size == 0:
        logging.error(f"從 {train_dir} 未載入任何訓練資料。")
        return None

    classifier = train_classifier(X, y, classifier_config)
    if classifier:
        logging.info(f"--- [{side.upper()}] 側模型訓練成功 ---")
    else:
        logging.error(f"--- [{side.upper()}] 側模型訓練失敗 ---")
        
    return classifier

def classify_and_move_images(side, config, rois, clf):
    """使用訓練好的模型對指定側的影像進行分類和移動"""
    logging.info(f"--- 開始處理 [{side.upper()}] 側的待分類影像 ---")
    dist_dir = os.path.join(side, 'dist')
    
    if not os.path.isdir(dist_dir):
        logging.warning(f"待分類資料夾 {dist_dir} 不存在，跳過。")
        return
    if clf is None:
        logging.error(f"[{side.upper()}] 側的模型不存在，無法進行分類。")
        return
    if not rois:
        logging.error(f"[{side.upper()}] 側未定義ROI，無法進行分類。")
        return

    smoke_out_dir = os.path.join(dist_dir, 'smoke')
    no_smoke_out_dir = os.path.join(dist_dir, 'no_smoke')
    os.makedirs(smoke_out_dir, exist_ok=True)
    os.makedirs(no_smoke_out_dir, exist_ok=True)

    feature_config = config['Features']
    fixed_size = (config.getint('Detection', 'fixed_image_width', fallback=128),
                  config.getint('Detection', 'fixed_image_height', fallback=128))
    
    image_files = [f for f in os.listdir(dist_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    logging.info(f"在 {dist_dir} 中找到 {len(image_files)} 張待分類影像。")

    for filename in image_files:
        file_path = os.path.join(dist_dir, filename)
        image = cv2.imread(file_path)
        if image is None: continue

        all_roi_features = []
        for roi in rois:
            feats = extract_features(image, roi_vertices=roi, feature_config=feature_config, fixed_size=fixed_size)
            if feats is not None:
                all_roi_features.append(feats)
        
        if not all_roi_features:
            logging.warning(f"無法從影像 {filename} 萃取特徵，跳過。")
            continue
            
        final_features = np.mean(all_roi_features, axis=0).reshape(1, -1)

        try:
            prediction = clf.predict(final_features)[0]
            dest_path = os.path.join(smoke_out_dir, filename) if prediction == 1 else os.path.join(no_smoke_out_dir, filename)
            label = "煙霧" if prediction == 1 else "無煙霧"
            shutil.move(file_path, dest_path)
            logging.info(f"影像 '{filename}' 已分類為 [{label}] 並移動。")
        except Exception as e:
            logging.error(f"處理影像 {filename} 時發生錯誤: {e}", exc_info=True)
            
    logging.info(f"--- [{side.upper()}] 側影像分類完成 ---")
#</editor-fold>

# --- GUI 相關程式碼 ---

class QueueHandler(logging.Handler):
    """將日誌紀錄發送到 queue 的 Handler"""
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))


class SmokeClassifierApp(ttk.Window):
    def __init__(self, theme='lumen'):
        super().__init__(themename=theme)
        self.title("煙霧偵測模型訓練 & 分類工具")
        self.geometry("900x700")

        self.config = None
        self.left_clf = None
        self.right_clf = None
        
        self.log_queue = queue.Queue()
        self.queue_handler = QueueHandler(self.log_queue)
        
        # 設定全域 logger
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%H:%M:%S')
        self.queue_handler.setFormatter(formatter)
        logger.addHandler(self.queue_handler)

        self._create_widgets()
        self.after(100, self.poll_log_queue)

        # 初始載入設定
        self.load_app_config()
        
    def _create_widgets(self):
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        main_frame.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 左側面板
        left_panel = self._create_side_panel(main_frame, "左側攝影機 (Left Camera)", "left")
        left_panel.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        # 右側面板
        right_panel = self._create_side_panel(main_frame, "右側攝影機 (Right Camera)", "right")
        right_panel.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        # 底部控制與日誌面板
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=10)
        bottom_frame.columnconfigure(0, weight=1)

        self.run_all_button = ttk.Button(
            bottom_frame,
            text="執行全部流程 (訓練 > 分類)",
            command=lambda: self.start_task(self.run_all_task),
            bootstyle=SUCCESS
        )
        self.run_all_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        log_frame = ttk.Labelframe(main_frame, text="執行日誌", padding=5)
        log_frame.grid(row=2, column=0, columnspan=2, sticky="nsew", pady=5)
        log_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=3)

        self.log_text = scrolledtext.ScrolledText(log_frame, state='disabled', height=15, font=("Microsoft YaHei", 9))
        self.log_text.pack(fill=BOTH, expand=True)

    def _create_side_panel(self, parent, title, side):
        panel = ttk.Labelframe(parent, text=title, padding=15)
        panel.columnconfigure(0, weight=1)

        # 將元件儲存在 self 的屬性中，以便後續存取
        status_label = ttk.Label(panel, text="模型未訓練", bootstyle=SECONDARY)
        status_label.grid(row=0, column=0, pady=(0, 10))
        setattr(self, f"{side}_status_label", status_label)
        
        progressbar = ttk.Progressbar(panel, mode='determinate', bootstyle=STRIPED)
        setattr(self, f"{side}_progressbar", progressbar)
        # 暫不顯示進度條，需要時再顯示
        
        train_button = ttk.Button(
            panel, text="1. 訓練模型",
            command=lambda: self.start_task(self.run_train_task, side),
            bootstyle=PRIMARY
        )
        train_button.grid(row=2, column=0, pady=5, sticky="ew")
        setattr(self, f"{side}_train_button", train_button)

        classify_button = ttk.Button(
            panel, text="2. 分類影像",
            command=lambda: self.start_task(self.run_classify_task, side),
            bootstyle=INFO
        )
        classify_button.grid(row=3, column=0, pady=5, sticky="ew")
        setattr(self, f"{side}_classify_button", classify_button)

        return panel

    def load_app_config(self):
        """載入應用程式設定檔"""
        self.config = load_config()
        if self.config is None:
            self.log_to_widget("錯誤：找不到或無法解析 config.ini，請檢查檔案是否存在。", "error")
            # 禁用所有按鈕
            self.toggle_all_buttons(False)

    def start_task(self, task_func, *args):
        """啟動一個背景任務"""
        # 禁用所有按鈕以防重複點擊
        self.toggle_all_buttons(False)
        
        # 顯示並啟動對應的進度條
        if args: # 如果有 side 參數
            side = args[0]
            p_bar = getattr(self, f"{side}_progressbar")
            p_bar.grid(row=1, column=0, pady=10, sticky="ew")
            p_bar.start()
        
        # 創建並啟動執行緒
        thread = threading.Thread(target=task_func, args=args, daemon=True)
        thread.start()

    def task_finished(self, side=None):
        """任務完成後的回呼函式，在主執行緒中執行"""
        self.toggle_all_buttons(True)
        if side:
            p_bar = getattr(self, f"{side}_progressbar")
            p_bar.stop()
            p_bar.grid_remove() # 隱藏進度條

    def run_train_task(self, side):
        """執行訓練模型的背景任務"""
        try:
            roi_path = self.config.get(f'{side.capitalize()}Camera', 'roi_config_path')
            rois = load_roi_from_config(roi_path)
            
            if not rois:
                logging.error(f"[{side.upper()}] 無法載入 ROI，訓練中止。")
                return

            classifier = train_model_for_side(side, self.config, rois)
            
            if classifier:
                if side == 'left':
                    self.left_clf = classifier
                else:
                    self.right_clf = classifier
                # 使用 self.after 在主執行緒中更新 UI
                self.after(0, self.update_status_label, side, "模型已訓練", SUCCESS)
            else:
                self.after(0, self.update_status_label, side, "模型訓練失敗", DANGER)

        except Exception as e:
            logging.error(f"[{side.upper()}] 訓練任務出錯: {e}\n{traceback.format_exc()}")
            self.after(0, self.update_status_label, side, "訓練出錯", DANGER)
        finally:
            self.after(0, self.task_finished, side)

    def run_classify_task(self, side):
        """執行分類影像的背景任務"""
        try:
            clf = self.left_clf if side == 'left' else self.right_clf
            if clf is None:
                logging.error(f"[{side.upper()}] 模型尚未訓練，無法分類。")
                return

            roi_path = self.config.get(f'{side.capitalize()}Camera', 'roi_config_path')
            rois = load_roi_from_config(roi_path)
            
            classify_and_move_images(side, self.config, rois, clf)
            self.after(0, self.update_status_label, side, "分類完成", SUCCESS)

        except Exception as e:
            logging.error(f"[{side.upper()}] 分類任務出錯: {e}\n{traceback.format_exc()}")
            self.after(0, self.update_status_label, side, "分類出錯", DANGER)
        finally:
            self.after(0, self.task_finished, side)
            
    def run_all_task(self):
        """執行所有流程的背景任務"""
        try:
            logging.info("===== 開始執行全部流程 =====")
            # 1. 訓練左模型
            self.after(0, lambda: self.left_progressbar.grid(row=1, column=0, pady=10, sticky="ew"))
            self.after(0, lambda: self.left_progressbar.start())
            self.run_train_task('left')
            self.after(0, lambda: self.left_progressbar.stop())
            self.after(0, lambda: self.left_progressbar.grid_remove())

            # 2. 訓練右模型
            self.after(0, lambda: self.right_progressbar.grid(row=1, column=0, pady=10, sticky="ew"))
            self.after(0, lambda: self.right_progressbar.start())
            self.run_train_task('right')
            self.after(0, lambda: self.right_progressbar.stop())
            self.after(0, lambda: self.right_progressbar.grid_remove())

            # 3. 分類左側影像
            self.after(0, lambda: self.left_progressbar.grid(row=1, column=0, pady=10, sticky="ew"))
            self.after(0, lambda: self.left_progressbar.start())
            self.run_classify_task('left')
            self.after(0, lambda: self.left_progressbar.stop())
            self.after(0, lambda: self.left_progressbar.grid_remove())

            # 4. 分類右側影像
            self.after(0, lambda: self.right_progressbar.grid(row=1, column=0, pady=10, sticky="ew"))
            self.after(0, lambda: self.right_progressbar.start())
            self.run_classify_task('right')
            self.after(0, lambda: self.right_progressbar.stop())
            self.after(0, lambda: self.right_progressbar.grid_remove())
            
            logging.info("===== 全部流程執行完畢 =====")
            
        except Exception as e:
            logging.error(f"[全部流程] 任務出錯: {e}\n{traceback.format_exc()}")
        finally:
            self.after(0, self.task_finished)


    def update_status_label(self, side, text, style):
        """安全地更新狀態標籤"""
        label = getattr(self, f"{side}_status_label")
        label.config(text=text, bootstyle=style)

    def toggle_all_buttons(self, enabled):
        """啟用或禁用所有按鈕"""
        state = NORMAL if enabled else DISABLED
        self.left_train_button.config(state=state)
        self.left_classify_button.config(state=state)
        self.right_train_button.config(state=state)
        self.right_classify_button.config(state=state)
        self.run_all_button.config(state=state)

    def poll_log_queue(self):
        """定期檢查日誌佇列並更新文字方塊"""
        while True:
            try:
                record = self.log_queue.get(block=False)
            except queue.Empty:
                break
            else:
                self.log_to_widget(record)
        self.after(100, self.poll_log_queue)

    def log_to_widget(self, message, level='info'):
        """將訊息插入到日誌文字方塊"""
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, message + '\n')
        self.log_text.see(tk.END) # 自動滾動到底部
        self.log_text.config(state='disabled')


if __name__ == "__main__":
    # 建立一個日誌檔案來記錄 GUI 運行時的任何問題
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[logging.FileHandler("gui_app_log.log", mode='w', encoding='utf-8')])
    
    app = SmokeClassifierApp(theme='superhero') # 可嘗試其他主題: "lumen", "darkly", "cosmo", "flatly"
    app.mainloop()

