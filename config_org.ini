[General]
# 日誌設定 (日誌檔案名在程式中固定為 smoke_detection.log)
log_level = INFO

# 狀態儲存檔案路徑
state_file = smoke_state.npz

# 預設偵測時間 (格式 HH:MM, 當 sun_date.csv 找不到或日期不存在時使用)
default_sunrise = 06:00
default_sunset = 19:00
# sun_date.csv 檔案路徑
sun_times_csv = sun_date.csv

# 看門狗檢查間隔 (秒)
watchdog_interval = 30

# 應用重啟所需的無響應時間 (秒)
watchdog_restart_timeout = 60

[LeftCamera]
# RTSP 或視訊檔案路徑
video_path = C:\stanley\HL_CCTV\HL_CCTV\output.mp4
# 訓練資料集目錄
dataset_dir = ./left_dataset
# 特徵快取目錄
cache_dir = ./left_cache
# ROI 設定檔路徑
roi_config_path = ./left_config.json
# 偵測到高機率煙霧時儲存圖片的目錄
image_save_dir = smoke_image_left

[RightCamera]
# RTSP 或視訊檔案路徑
video_path = C:\stanley\HL_CCTV\HL_CCTV\1747-3.mp4
# 訓練資料集目錄
dataset_dir = ./right_dataset
# 特徵快取目錄
cache_dir = ./right_cache
# ROI 設定檔路徑
roi_config_path = ./right_config.json
# 偵測到高機率煙霧時儲存圖片的目錄
image_save_dir = smoke_image_right

[Detection]
# 處理影格並執行偵測的間隔 (秒)
process_interval = 1.0

# 偵測到高機率煙霧時儲存圖片的最小間隔 (秒)
save_interval_high_prob = 60.0

# 偵測器視窗顯示的固定影像尺寸 (寬,高)
fixed_image_width = 128
fixed_image_height = 128

# 煙霧判定閾值 (機率 >= 此值則判為煙霧)
smoke_threshold = 0.5

# 事件最小持續時間 (秒, 連續 >= smoke_threshold 超過此時間才算一個事件)
# 此值也用於觸發郵件寄送 (需持續 5 分鐘即 300 秒)
min_event_duration = 5 

# 觸發每日計數的較高機率閾值
high_prob_threshold_for_daily_count = 0.95 

# 同一事件每隔多少分鐘再次計數 (分鐘)
daily_count_interval_minutes = 5

[Classifier]
# SVM 核函數 (linear, poly, rbf, sigmoid, precomputed)
svm_kernel = linear
# 是否啟用機率計算 (True/False)
svm_probability = True

[Features]
# 是否使用 HOG 特徵 (True/False)
use_hog = True
# HOG 參數 (pixels_per_cell_w,pixels_per_cell_h,cells_per_block_w,cells_per_block_h)
hog_params = 16,16,2,2

# 是否使用 LBP 特徵 (True/False)
use_lbp = True
# LBP 參數 (P, R, method)
lbp_params = 8,1,uniform

# 是否使用 SIFT 特徵 (True/False)
# 注意: SIFT 是非自由軟體，可能需要額外安裝 opencv-contrib-python
use_sift = False

[EmailSettings]
# 是否啟用郵件寄送功能 (True/False)
send_email_enabled = False

# 郵件發送 API 端點 URL
api_url = http://192.168.14.14/Datarelay/SendMailByJsonHL

# 郵件收件人 (多個收件人以逗號分隔)
mail_to = 0505462

# 郵件副本收件人 (多個收件人以逗號分隔, 可留空)
mail_cc = 0803284,1706104

# 郵件標題模板 ({side} 和 {time} 會被替換)
subject_template = HL-CCTV {side} 煙霧偵測警報 ({time})

# 郵件內文模板 ({side}, {time}, {duration}, {severity} 會被替換)
body_template = 偵測到{side}攝影機區域發生煙霧。\n事件開始時間:{time}\n持續時間:{duration:.1f}秒\n平均嚴重度:{severity:.2f}\n請立即查看影像確認狀況。

# 系統識別碼
system_name = HL-CCTV

# 使用者識別碼
user_id = 0803284

# 同一攝影機郵件發送的最小間隔 (秒)，防止短時間內重複發送
min_email_interval = 600 
# 設定為 600 秒 (10 分鐘)