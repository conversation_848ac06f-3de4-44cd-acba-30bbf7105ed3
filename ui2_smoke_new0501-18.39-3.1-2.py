# -*- coding: utf-8 -*-
print("Script started")
import cv2
import os
import sys
import csv
import numpy as np
import json
import time
import datetime
import logging
import traceback
import threading
import warnings
import gc
from collections import deque, OrderedDict
from skimage.feature import hog, local_binary_pattern
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw, ImageFont # 導入 ImageDraw 和 ImageFont 以便在PIL圖像上繪製中文
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import csv
import configparser
import requests # 導入 requests 模組
import tkinter.font as tkFont # 導入 tkinter.font
import matplotlib.colorbar # 導入 matplotlib.colorbar 以便檢查colorbar實例

# ============================
# 導入 PyQt5 模組 (來自 setup_ini3.py)
# ============================
import re # 用於正則表達式解析 key=value
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QFormLayout, QLineEdit, QTextEdit, QCheckBox, QPushButton,
    QScrollArea, QGroupBox, QLabel, QStatusBar,
    QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


# ============================
# 設定日誌紀錄功能 (改為只取得 logger 實例，詳細配置在 load_config 中)
# ============================
# 在全域範圍取得 logger 實例
global logger
logger = logging.getLogger("SmokeDetection")
# 在 main 入口點會有一個初始配置，load_config 會重新配置它

# ============================
# Matplotlib 和 Tkinter 中文字體設定
# ============================
# 選擇適合您操作系統的中文字體
# 對於 Windows: 'Microsoft YaHei', 'SimHei', 'FangSong', 'SimSun'
# 對於 macOS: 'PingFang SC', 'Heiti SC'
# 對於 Linux: 'Noto Sans CJK SC', 'WenQuanYi Zen Hei'
CHINESE_FONT = 'Microsoft YaHei' # 您可以根據實際情況修改此處

# 嘗試加載字體文件，如果無法加載，則使用回退方案
try:
    # 針對 Windows 系統，微軟雅黑通常是 TrueType 字體
    # 這條路徑可能需要根據您的系統實際字體文件路徑來調整
    FONT_PATH_FOR_OPENCV = "C:/Windows/Fonts/msyh.ttc" # 微軟雅黑的TTF文件路徑
    if not os.path.exists(FONT_PATH_FOR_OPENCV):
        # 嘗試其他常見字體，例如黑體
        FONT_PATH_FOR_OPENCV = "C:/Windows/Fonts/simhei.ttf"
    if not os.path.exists(FONT_PATH_FOR_OPENCV):
        logger.warning(f"未能找到指定的中文字體文件用於OpenCV繪製: {FONT_PATH_FOR_OPENCV}。OpenCV文字可能無法顯示中文。")
        FONT_PATH_FOR_OPENCV = None # 設置為None，表示無法使用字體文件
except Exception as e:
    logger.error(f"獲取中文字體文件路徑時發生錯誤: {e}", exc_info=True)
    FONT_PATH_FOR_OPENCV = None


plt.rcParams['font.sans-serif'] = [CHINESE_FONT] + plt.rcParams['font.sans-serif'] # 將中文黑體放在最前面
plt.rcParams['axes.unicode_minus'] = False # 解決負號'-'顯示為方塊的問題

# ============================
# 載入配置檔
# ============================

def load_config(config_file='config.ini'):
    """載入配置檔並設定相關參數"""
    try:
        logger.info("load_config() 開始")
    except Exception as e:
        # Fallback print if logger still fails even after initial setup
        print(f"Error during initial logger.info in load_config: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        sys.exit(1)

    config = configparser.ConfigParser()

    if not os.path.exists(config_file):
        logger.error(f"組態檔案未找到: {config_file}")
        # create_default_config(config_file) # Assuming this function exists elsewhere or is not needed here
        logger.info(f"已建立預設組態檔案: {config_file}。請編輯它。")
        return None # 返回 None 表示載入失敗

    try:
        # Added explicit encoding for config.read() to handle UTF-8 characters correctly
        config.read(config_file, encoding='utf-8')
        logger.info(f"組態已從 {config_file} 載入")

        # 設定日誌等級
        log_level_str = config.get('General', 'log_level', fallback='INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)

        # 重新配置全域日誌的處理器
        # Remove existing handlers to avoid duplicates and ensure clean reconfiguration
        for handler in logger.handlers[:]:
             logger.removeHandler(handler)

        # 添加已配置的處理器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler = logging.FileHandler("smoke_detection.log", mode='a', encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

        # 設定日誌等級
        logger.setLevel(log_level)
        logger.info(f"日誌等級已設定為 {log_level_str}")

        # 設定FFMPEG參數 (如果 config 包含)
        if config.has_section('FFMPEG') and config.has_option('FFMPEG', 'capture_options') and 'capture_options' in config['FFMPEG']:
             options = config.get('FFMPEG', 'capture_options')
             os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = options
             logger.info(f"FFMPEG 擷取選項已設定: {options}")
        else:
             # 使用預設值
             os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "rtsp_transport;tcp|analyzeduration;0|fflags;nobuffer|stimeout;1000000"
             logger.info("正在使用預設 FFMPEG 擷取選項。")

        # 忽略部分警告
        warnings.filterwarnings("ignore", category=RuntimeWarning)
        warnings.filterwarnings("ignore", category=UserWarning)
        warnings.filterwarnings("ignore", category=FutureWarning)

        # 將配置物件直接返回，方便在 main 中使用
        logger.info("load_config() 結束")
        return config
    
    except configparser.Error as e:
        logger.error(f"解析組態檔案 {config_file} 錯誤: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"載入組態檔案 {config_file} 錯誤: {traceback.format_exc()}", exc_info=True)
        return None

# ============================
# 幫助函式：格式化日期字串
# ============================
def format_date(d):
    s = d.strftime("%m/%d")
    # 移除月份前的零 (如果存在)
    if s.startswith("0"):
        s = s[1:]
    # 移除日期前的零 (如果存在)
    parts = s.split("/")
    if len(parts) == 2:
       parts[1] = parts[1].lstrip("0")
       s = "/".join(parts)
    return s

# ============================
# 1. 特徵萃取函式 (參數從 config 傳入)
# ============================
def extract_features(image, roi_vertices=None, feature_config=None, fixed_size=(128,128)):
    try:
        if image is None:
            return None

        # 使用 config 提供的 feature_config
        if feature_config is None:
            logger.error("未提供特徵組態。")
            return None

        use_hog = feature_config.getboolean('use_hog', False)
        hog_params_str = feature_config.get('hog_params', '16,16,2,2').split(',')
        try:
            hog_ppc = (int(hog_params_str[0]), int(hog_params_str[1]))
            hog_cpb = (int(hog_params_str[2]), int(hog_params_str[3]))
        except (ValueError, IndexError):
             logger.error(f"組態中 hog_params 格式無效: {hog_params_str}。使用預設值 16,16,2,2", exc_info=True)
             hog_ppc = (16, 16)
             hog_cpb = (2, 2)

        use_lbp = feature_config.getboolean('use_lbp', False)
        lbp_params_str = feature_config.get('lbp_params', '8,1,uniform').split(',')
        try:
            lbp_p = int(lbp_params_str[0])
            lbp_r = int(lbp_params_str[1])
            lbp_method = lbp_params_str[2] if len(lbp_params_str) > 2 else 'uniform'
        except (ValueError, IndexError):
             logger.error(f"組態中 lbp_params 格式無效: {lbp_params_str}。使用預設值 8,1,uniform", exc_info=True)
             lbp_p = 8
             lbp_r = 1
             lbp_method = 'uniform'

        use_sift = feature_config.getboolean('use_sift', False)

        # 處理 ROI 或整個影像
        if roi_vertices is not None:
            roi_vertices_array = np.array(roi_vertices, dtype=np.int32)
            # 建立遮罩
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            # 繪製前確保輪廓有效
            if len(roi_vertices_array) >= 3:
                # 如果需要 drawContours 接受 (N, 1, 2) 格式，則重塑 roi_vertices_array
                # drawContours 也接受 (N, 2)，但 (N, 1, 2) 是標準
                cv2.drawContours(mask, [roi_vertices_array.reshape((-1, 1, 2))], -1, 255, thickness=cv2.FILLED)
            else:
                logger.warning(f"提供的 ROI 頂點數量無效 ({len(roi_vertices_array)})，已跳過。")
                return None

            # 應用遮罩並裁切邊界框
            roi_image = cv2.bitwise_and(image, image, mask=mask)
            # 僅在遮罩不為全黑時計算邊界矩形
            if np.any(mask):
                 x, y, w, h = cv2.boundingRect(mask)
                 # 確保裁切在範圍內且尺寸有效
                 x = max(0, x)
                 y = max(0, y)
                 w = min(w, image.shape[1] - x)
                 h = min(h, image.shape[0] - y)

                 if w > 0 and h > 0:
                      roi_image_cropped = roi_image[y:y+h, x:x+w]
                 else:
                      logger.warning("邊界矩形後的 ROI 尺寸無效 (寬或高 <=0)。無法裁切。")
                      return None # ROI 區域無效
            else:
                 logger.warning("ROI 遮罩為全黑。沒有有效區域。")
                 return None # 遮罩為空

            # 調整裁切後的 ROI 尺寸
            if roi_image_cropped.shape[0] > 0 and roi_image_cropped.shape[1] > 0:
                 image_processed = cv2.resize(roi_image_cropped, fixed_size)
            else:
                 logger.warning("裁切後的 ROI 影像尺寸為零。無法調整尺寸。")
                 return None # 裁切尺寸為零
        else:
            # 如果沒有 ROI，則調整整個影像尺寸
            image_processed = cv2.resize(image, fixed_size)

        # 轉換為灰度影像以進行特徵萃取
        if len(image_processed.shape) == 3 and image_processed.shape[2] == 3:
            gray = cv2.cvtColor(image_processed, cv2.COLOR_BGR2GRAY)
        else:
            gray = image_processed # 假設輸入已為灰度影像

        features = []

        if use_hog:
            try:
                # 檢查灰度影像是否為空
                if gray.shape[0] > 0 and gray.shape[1] > 0:
                     hog_features = hog(gray, pixels_per_cell=hog_ppc, cells_per_block=hog_cpb, visualize=False)
                     features.append(hog_features)
                else:
                     logger.warning("灰度影像為空，無法進行 HOG 特徵萃取。")

            except Exception as hog_e:
                 logger.error(f"HOG 特徵萃取錯誤: {hog_e}", exc_info=True)

        if use_lbp:
            try:
                if gray.shape[0] > 0 and gray.shape[1] > 0:
                    lbp = local_binary_pattern(gray, P=lbp_p, R=lbp_r, method=lbp_method)
                    # 根據方法和 P 計算適當的 bins 數量
                    if lbp_method == 'uniform':
                        n_bins = lbp_p + 2
                    elif lbp_method == 'nri_uniform':
                         n_bins = lbp_p*(lbp_p-1) + 3
                    else:
                        # 對於基本方法，直方圖涵蓋所有 2^P 值
                        n_bins = 2**lbp_p
                    # 確保直方圖範圍與 bins 匹配
                    (hist, _) = np.histogram(lbp.ravel(), bins=n_bins, range=(0, n_bins))
                    hist = hist.astype("float")
                    hist /= (hist.sum() + 1e-6)
                    features.append(hist)
                else:
                     logger.warning("灰度影像為空，無法進行 LBP 特徵萃取。")
            except Exception as lbp_e:
                logger.error(f"LBP 特徵萃取錯誤: {lbp_e}", exc_info=True)

        if use_sift:
            try:
                if not hasattr(cv2, 'SIFT_create'):
                     logger.warning("請求使用 SIFT，但未安裝 (需要 opencv-contrib-python)。已跳過 SIFT。")
                     # 無需全域設定 use_sift = False，僅在此實例中跳過
                elif gray.shape[0] > 0 and gray.shape[1] > 0:
                    sift = cv2.SIFT_create()
                    keypoints, descriptors = sift.detectAndCompute(gray, None)
                    if descriptors is not None and descriptors.shape[0] > 0:
                        sift_features = np.mean(descriptors, axis=0)
                        features.append(sift_features)
                    else:
                        # 如果未找到關鍵點，返回零向量，但仍追加以保持特徵維度 (如果 SIFT 已啟用)
                        logger.debug("影像中未找到 SIFT 關鍵點。")
                        # 即使未找到描述符，仍需要預期的維度。SIFT 描述符為 128 維。
                        features.append(np.zeros(128))
                else:
                     logger.warning("灰度影像為空，無法進行 SIFT 特徵萃取。")

            except Exception as sift_e:
                logger.error(f"SIFT 特徵萃取錯誤: {traceback.format_exc()}", exc_info=True)
                # 決定如果某個特徵萃取失敗，是否完全中止萃取。
                # 目前，我們繼續並只追加成功萃取的特徵。

        # 檢查是否有任何特徵成功萃取
        if not features:
             # logger.warning("未啟用或未成功萃取任何特徵。") # 訊息過於詳細
             return None

        return np.concatenate(features)
    except Exception as e:
        logger.error(f"特徵萃取錯誤: {traceback.format_exc()}", exc_info=True)
        return None

# ============================
# 2. 資料集讀取與特徵萃取 (參數從 config 傳入)
# ============================
def load_dataset(dataset_dir, rois, feature_config, fixed_size):
    data = []
    labels = []
    logger.info(f"正在從 {dataset_dir} 載入資料集")
    # 確保 dataset_dir 存在
    if not os.path.exists(dataset_dir):
        logger.error(f"資料集目錄未找到: {dataset_dir}")
        return np.array(data), np.array(labels)

    for label_dir in ['smoke', 'no_smoke']:
        dir_path = os.path.join(dataset_dir, label_dir)
        if not os.path.exists(dir_path):
            logger.warning(f"目錄 {dir_path} 不存在")
            continue
        file_count = 0
        target_label = 1 if label_dir=="smoke" else 0
        image_files = [f for f in os.listdir(dir_path) if f.lower().endswith(('.jpg','.jpeg','.png','.bmp'))]
        logger.info(f"在 {label_dir} 目錄中找到 {len(image_files)} 張影像。")
        
        for filename in image_files:
            file_path = os.path.join(dir_path, filename)
            image = cv2.imread(file_path)
            if image is None:
                logger.warning(f"無法載入影像 {file_path}")
                continue
            try:
                features = None # 初始化影像的特徵
                if rois is not None and len(rois) > 0:
                     all_roi_features = []
                     for i, roi in enumerate(rois):
                         # 在萃取前檢查 ROI 是否有效
                         if not (isinstance(roi, list) and len(roi) >= 3 and all(isinstance(p, list) and len(p) == 2 for p in roi)):
                             logger.warning(f"影像 {filename} 的 ROI {i} 格式無效。已跳過此 ROI。")
                             continue # 跳過此特定 ROI

                         feats = extract_features(image, roi_vertices=roi, feature_config=feature_config, fixed_size=fixed_size)
                         if feats is not None:
                             all_roi_features.append(feats)

                     # 聚合來自多個 ROI 的特徵 (使用平均值)
                     if all_roi_features:
                         # 在取平均值之前檢查所有特徵向量是否具有相同的形狀
                         feature_shapes = [f.shape for f in all_roi_features]
                         if len(set(feature_shapes)) == 1:
                             features = np.mean(all_roi_features, axis=0)
                         else:
                             logger.error(f"影像 {filename} 的 ROI 特徵形狀不一致: {feature_shapes}。無法聚合。")
                             continue # 如果特徵形狀不一致，則跳過影像
                     else:
                         logger.warning(f"在 {file_path} 中沒有從任何 ROI 萃取到特徵。已跳過影像。")
                         continue # 如果沒有有效的 ROI 特徵，則跳過影像
                else:
                    # 如果未定義 ROI，則從整個影像中萃取特徵
                    logger.info(f"未定義 ROI，正在從整個影像 {filename} 中萃取特徵。")
                    features = extract_features(image, roi_vertices=None, feature_config=feature_config, fixed_size=fixed_size)
                    if features is None:
                        logger.warning(f"沒有從整個影像 {file_path} 萃取到特徵。已跳過。")
                        continue

                # 確保 features 是一個 numpy 陣列且長度不為零
                if isinstance(features, np.ndarray) and features.size > 0:
                     data.append(features)
                     labels.append(target_label)
                     file_count += 1
                else:
                     logger.warning(f"特徵萃取導致 {file_path} 的資料為空或無效。已跳過。")

            except Exception as e:
                logger.error(f"處理 {file_path} 錯誤: {traceback.format_exc()}", exc_info=True)
        logger.info(f"成功處理 {label_dir} 目錄中的 {file_count} 張影像")

    logger.info(f"完成從 {dataset_dir} 載入資料集。總樣本數: {len(data)}")
    return np.array(data), np.array(labels)

# ============================
# 3. 快取功能：存取萃取的特徵 (cache_dir 從 config 傳入)
# ============================
def cache_features(cache_dir, X, y):
    try:
        if not os.path.exists(cache_dir):
             os.makedirs(cache_dir, exist_ok=True)

        if X is None or y is None or len(X) == 0 or len(X) != len(y):
             logger.warning("沒有資料可快取或資料無效。")
             return

        # 使用 gzip 壓縮的 .npz 格式
        cache_path = os.path.join(cache_dir, "features.npz") # 修改為 .npz
        np.savez_compressed(cache_path, X=X, y=y)
        logger.info(f"特徵已快取到 {cache_path}")
    except Exception as e:
        logger.error(f"快取特徵到 {cache_dir} 錯誤: {traceback.format_exc()}", exc_info=True)

def load_cached_features(cache_dir):
    try:
        cache_path = os.path.join(cache_dir, "features.npz") # 修改為 .npz
        if not os.path.exists(cache_path):
            logger.info(f"快取檔案未找到於 {cache_path}")
            return np.array([]), np.array([])
        npzfile = np.load(cache_path, allow_pickle=True)
        X = npzfile['X']
        y = npzfile['y']
        npzfile.close() # 關閉檔案句柄

        if len(X) == 0:
             logger.warning(f"快取特徵檔案 {cache_path} 為空。")

        logger.info(f"已從 {cache_dir} 載入 {len(X)} 個快取特徵")
        return X, y
    except FileNotFoundError:
         logger.info(f"快取檔案未找到: {cache_dir}/features.npz") # 修改為 .npz
         return np.array([]), np.array([])
    except Exception as e:
        logger.error(f"從 {cache_dir} 載入快取特徵錯誤: {traceback.format_exc()}", exc_info=True)
        # 錯誤時返回空陣列
        return np.array([]), np.array([])

# ============================
# 4. 分類器訓練與預測 (參數從 config 傳入)
# ============================
def train_classifier(X, y, classifier_config):
    try:
        if len(X) == 0 or len(y) == 0:
            logger.warning("沒有資料可訓練分類器。")
            return None

        # 檢查最小樣本數以進行分割和唯一類別
        num_samples = len(X)
        unique_classes = np.unique(y)
        num_classes = len(unique_classes)

        if num_samples < 2:
             logger.error(f"訓練資料僅有 {num_samples} 個樣本。無法訓練。")
             return None

        svm_probability = classifier_config.getboolean('svm_probability', True)
        if svm_probability and num_classes < 2:
            logger.error(f"僅找到 {num_classes} 個唯一類別 ({unique_classes})。無法在 probability=True 的情況下訓練，這至少需要兩個類別。")
            return None

        # 僅當有足夠的樣本用於訓練集和測試集時才分割資料
        if num_samples >= 5: # 啟發式: 訓練集和測試集需要一些樣本
            try:
                 X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y if num_classes > 1 else None)
            except ValueError as e:
                 logger.warning(f"無法執行分層分割: {e}。退回簡單分割。")
                 X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            logger.info(f"分割資料: 訓練集={len(X_train)} 個樣本, 測試集={len(X_test)} 個樣本。")
        else:
            logger.warning(f"只有 {num_samples} 個樣本。在整個資料集上進行訓練，不執行測試分割。")
            X_train, y_train = X, y
            X_test, y_test = np.array([]), np.array([]) # 空測試集

        # 從 config 讀取 SVM 參數
        svm_kernel = classifier_config.get('svm_kernel', 'linear')

        logger.info(f"正在訓練 SVC，核函數='{svm_kernel}'，機率={svm_probability}")

        clf = SVC(kernel=svm_kernel, probability=svm_probability, random_state=42)
        clf.fit(X_train, y_train)

        # 驗證集評估 (如果測試集有數據)
        if len(X_test) > 0:
            y_pred = clf.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"測試準確度: {accuracy:.4f}")
            try:
                # 確保 target_names 與 y_test 或 y_train 中找到的唯一類別的順序匹配
                # 或者使用已擬合分類器的 classes 屬性
                target_names = [str(c) for c in clf.classes_]
                report = classification_report(y_test, y_pred, target_names=target_names, zero_division=0) # zero_division=0 處理預測中沒有樣本的類別
                logger.info("\n分類報告:\n" + report)
            except ValueError as e:
                 logger.warning(f"無法生成分類報告: {e}。可能是由於測試集中缺少類別或類別不符合預期。")
            except Exception as e:
                 logger.error(f"生成分類報告錯誤: {e}", exc_info=True)
        else:
            logger.warning("分割後沒有可用於評估的測試資料。")

        return clf
    except Exception as e:
        logger.error(f"訓練分類器錯誤: {traceback.format_exc()}", exc_info=True)
        return None

def predict_image_multiple_rois(image, rois, clf, feature_config, detection_config, classifier_config):
    if image is None or clf is None or not rois: # 新增對空 rois 的檢查
        # If no ROIs are defined, detection can't happen meaningfully.
        # Log this explicitly if it's unexpected to aid debugging.
        if not rois:
            logger.debug("predict_image_multiple_rois called with no ROIs defined. Returning no smoke.")
        return "無煙霧", [] # 如果沒有 ROI，返回空結果列表

    fixed_size = (detection_config.getint('fixed_image_width', 128),
                  detection_config.getint('fixed_image_height', 128))
    smoke_threshold = detection_config.getfloat('smoke_threshold', 0.5)
    # 修正: 從 classifier_config 獲取 svm_probability 設定
    svm_probability_enabled = classifier_config.getboolean('svm_probability', True)

    results = [] # 每個 ROI 的 (預測, 機率) 元組列表
    try:
        # 檢查分類器是否可以預測
        if not hasattr(clf, 'predict'):
             logger.error("分類器物件缺少 'predict' 方法。")
             # 根據 ROI 數量創建虛擬結果
             return "錯誤", [(0, 0.0)] * len(rois)

        if svm_probability_enabled and not hasattr(clf, 'predict_proba'):
             logger.warning("組態中啟用了分類器機率計算，但缺少 'predict_proba' 方法。將預測結果作為機率 (1.0 或 0.0)。")
             svm_probability_enabled = False # 如果方法缺失，則此運行禁用機率

        for i, roi in enumerate(rois):
            # 確保 roi 是一個有效的點列表
            if not (isinstance(roi, list) and len(roi) >= 3 and all(isinstance(p, list) and len(p)==2 for p in roi)):
                logger.warning(f"跳過 ROI 索引 {i} 的無效 ROI 格式: {roi}")
                results.append((0, 0.0)) # 為無效 ROI 添加預設結果
                continue

            feats = extract_features(image, roi_vertices=roi, feature_config=feature_config, fixed_size=fixed_size)
            if feats is None:
                # logger.debug(f"ROI 索引 {i} 未萃取到特徵。跳過此 ROI 的預測。") # 訊息過於詳細
                results.append((0, 0.0)) # 如果特徵萃取失敗，添加預設結果
                continue

            # 重塑特徵以進行預測
            feats = feats.reshape(1, -1)

            try:
                pred = clf.predict(feats)[0]

                if svm_probability_enabled:
                     prob = clf.predict_proba(feats)[0]
                     # 找到 'smoke' 類別機率的索引 (對應標籤 1)
                     # 確保 clf.classes_ 可用且包含 1
                     if hasattr(clf, 'classes_') and 1 in clf.classes_:
                         smoke_class_index = np.where(clf.classes_ == 1)[0][0] # 獲取索引
                         smoke_prob = prob[smoke_class_index]
                     else:
                          # 這種情況表示訓練資料中沒有類別 1，但應在更早的時候捕獲
                          logger.warning("在預測期間分類器類別中未找到煙霧類別 (標籤 1)。機率設定為 0。")
                          smoke_prob = 0.0
                          # 如果訓練中沒有類別 1，則預測 `pred` 也可能永遠不會是 1。
                else:
                     # 如果禁用了機率，直接使用預測結果，如果模型預測為 1，則機率設定為 1.0，否則為 0.0
                     smoke_prob = 1.0 if pred == 1 else 0.0

                # 根據 smoke_threshold 決定最終的 ROI 預測
                # 這個二元預測用於繪製 ROI 顏色和決定整體狀態
                final_roi_pred = 1 if smoke_prob >= smoke_threshold else 0

                results.append((final_roi_pred, smoke_prob))

            except Exception as predict_e:
                logger.error(f"ROI 索引 {i} 的預測錯誤: {predict_e}。跳過此 ROI 預測。", exc_info=True)
                results.append((0, 0.0)) # 預測錯誤時添加預設結果

        # 如果任何 ROI 預測基於閾值為 1，則整體標籤為 "Smoke"
        overall_label = "煙霧" if any(r[0] == 1 for r in results) else "無煙霧"
        return overall_label, results

    except Exception as e:
        logger.error(f"影像預測錯誤: {traceback.format_exc()}", exc_info=True)
        # 如果發生重大錯誤，返回預設 "無煙霧" 和所有 ROI 的零機率
        # 即使出錯，也要確保 results 列表與 rois 數量匹配
        return "預測錯誤", [(0, 0.0)] * len(rois) # 返回預設結果

# ============================
# 5. 從 JSON ROI 定義檔讀取 ROI 定義 (config_path 從 config 傳入)
# ============================
def load_roi_from_config(config_path):
    if not os.path.exists(config_path):
        logger.error(f"ROI 組態檔案未找到: {config_path}")
        return []
    try:
        with open(config_path, 'r', encoding='utf-8') as f: # 指定編碼
            data = json.load(f)
        rois = []
        for i, roi_dict in enumerate(data.get("rois", [])):
            vertices = roi_dict.get("vertices")
            # 允許多邊形 ROI (至少3個點)
            if (vertices and isinstance(vertices, list) and len(vertices) >= 3 and
                all(isinstance(pt, list) and len(pt)==2 for pt in vertices)):
                rois.append(vertices) # 保持為列表的列表，以便 drawContours 輸入格式
            else:
                logger.warning(f"索引 {i} 處的 ROI 定義無效 (必須是 >=3 個 [x, y] 點的列表)，已跳過: {roi_dict}")
        logger.info(f"已從 {config_path} 載入 {len(rois)} 個有效 ROI")
        return rois
    except json.JSONDecodeError as e:
        logger.error(f"從 ROI 組態檔案 {config_path} 解碼 JSON 錯誤: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"從 {config_path} 載入 ROI 組態錯誤: {traceback.format_exc()}", exc_info=True)
        return []

# ============================
# 幫助函式: 從 CSV 讀取日出日落時間
# ============================
def _read_sun_times_from_csv(csv_file_path):
    sun_times = {}
    if not os.path.exists(csv_file_path):
        logger.warning(f"找不到日出日落時間 CSV 檔案: {csv_file_path}")
        return sun_times
    try:
        with open(csv_file_path, mode='r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            # 防禦性地嘗試跳過標頭行
            try:
                first_row = next(csv_reader)
                if first_row[0].strip().lower() != 'date':
                    logger.warning(f"{csv_file_path} 中的第一行看起來不像標頭，反而有: {first_row}")
                    csvfile.seek(0) # 倒回開頭重新讀取
                else:
                    logger.debug(f"正在跳過 {csv_file_path} 中的標頭行。")

            except StopIteration:
                logger.warning(f"日出日落時間 CSV 檔案 {csv_file_path} 為空。")
                return sun_times  # 檔案為空

            for row in csv_reader:
                if len(row) >= 3:  # 允許有額外列
                    date_str, sunrise_str, sunset_str = row[:3]
                    try:
                        # 從 MM/DD 格式解析日期
                        month_day = list(map(int, date_str.strip().split('/')))
                        # 構造不含年份的日期鍵，直接使用 MM/DD 這樣的鍵
                        normalized_date_str = f"{month_day[0]}/{month_day[1]}"
                        sunrise_time = datetime.datetime.strptime(sunrise_str.strip(), '%H:%M').time()
                        sunset_time = datetime.datetime.strptime(sunset_str.strip(), '%H:%M').time()
                        sun_times[normalized_date_str] = (sunrise_time, sunset_time)
                    except ValueError as ve:
                        logger.warning(f"CSV 行 {row} 中的時間或日期格式錯誤: {ve}。跳過此行。")
                    except Exception as row_e:
                        logger.error(f"處理 CSV 行 {row} 錯誤: {row_e}。跳過此行。", exc_info=True)
                else:
                    logger.warning(f"CSV 行格式錯誤 (需要至少3列): {row}。跳過此行。")
        logger.info(f"已從日出日落時間 CSV 檔案 {csv_file_path} 載入 {len(sun_times)} 筆資料")
    except FileNotFoundError:
        logger.warning(f"日出日落時間 CSV 檔案未找到: {csv_file_path}")
    except Exception as e:
        logger.error(f"讀取 CSV 檔案 {csv_file_path} 錯誤: {traceback.format_exc()}", exc_info=True)
    return sun_times

# ============================
# 郵件發送功能
# ============================
def send_smoke_email(email_config, camera_side, event_time, duration, severity):
    """通過配置的 API 發送電子郵件通知。"""
    # 檢查是否啟用了郵件發送
    if not email_config.getboolean('send_email_enabled', False):
        # logger.debug("組態中已禁用郵件發送。") # 訊息過於詳細
        return

    api_url = email_config.get('api_url', None)
    mail_to = email_config.get('mail_to', None)
    mail_cc = email_config.get('mail_cc', '') # CC 是可選的
    subject_template = email_config.get('subject_template', '煙霧偵測警報 {side}')
    body_template = email_config.get('body_template', '攝影機 {side} 偵測到煙霧。')
    system_name = email_config.get('system_name', 'SmokeDetection')
    user_id = email_config.get('user_id', 'system') # 預設使用者

    if not api_url or not mail_to:
        logger.error(f"{camera_side} 攝影機: 電子郵件 API URL ({api_url}) 或收件人 (mail_to={mail_to}) 未配置。無法發送電子郵件。")
        return

    try:
        # 格式化主旨和內文與事件詳細資訊
        event_datetime_obj = datetime.datetime.fromtimestamp(event_time)
        formatted_subject = subject_template.format(
            side=camera_side.capitalize(),
            time=event_datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
        )
        formatted_body = body_template.format(
            side=camera_side.capitalize(),
            time=event_datetime_obj.strftime('%Y-%m-%d %H:%M:%S'),
            duration=duration,
            severity=severity
        )

        email_data = {
            "body": formatted_body,
            "mail_to": mail_to,
            "mail_cc": mail_cc,
            "subject": formatted_subject,
            "Sys": system_name,
            "Usr": user_id
        }

        payload = json.dumps(email_data)
        headers = {"Content-Type": "application/json"}

        logger.info(f"{camera_side} 攝影機: 嘗試發送煙霧警報電子郵件給 {mail_to} (副本: {mail_cc})...")
        # 為請求設置超時，防止掛起
        response = requests.post(api_url, headers=headers, data=payload, timeout=15) # 稍微增加超時時間

        # 檢查回應狀態碼
        response.raise_for_status() # 對於不良回應 (4xx 或 5xx) 拋出 HTTPError

        # 記錄成功和回應內容
        logger.info(f"{camera_side} 攝影機: 透過 API 成功發送電子郵件。回應狀態: {response.status_code}。回應內容: {response.text}")

    except requests.exceptions.Timeout:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: 請求在 15 秒後超時。")
    except requests.exceptions.ConnectionError as e:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: 連接 {api_url} 錯誤 - {e}")
    except requests.exceptions.HTTPError as e:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: HTTP 錯誤 - {e} (回應: {getattr(e.response, 'text', 'N/A')})")
    except requests.exceptions.RequestException as e:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: 一般請求錯誤 - {e}")
    except Exception as e:
        logger.error(f"{camera_side} 攝影機: 發送電子郵件期間發生意外錯誤: {traceback.format_exc()}", exc_info=True)

# ============================
# 6. VideoPanel 類別 (參數從 config 傳入)
# ============================
class VideoPanel(tk.Frame):
    def __init__(self, parent, config, side="left", state=None):
        # config 是 configparser 物件，side 是 "left" 或 "right"
        super().__init__(parent, bg='light green')
        self.config = config # 在 VideoPanel 內部，這個名稱沒有衝突
        self.side = side
        self.video_path = self.config.get(f'{self.side.capitalize()}Camera', 'video_path')
        self.roi_config_path = self.config.get(f'{self.side.capitalize()}Camera', 'roi_config_path')
        self.image_save_dir = self.config.get(f'{self.side.capitalize()}Camera', 'image_save_dir')

        self.rois = load_roi_from_config(self.roi_config_path)
        if not self.rois:
            logger.error(f"{self.side} 攝影機: 未從 {self.roi_config_path} 載入有效的 ROI。偵測可能已禁用。")
            # 如果 self.rois 為空，偵測將隱式禁用

        # 分類器將在訓練後傳入
        self.clf = None # 初始化為 None

        # 從 config 獲取偵測參數
        self.process_interval = self.config.getfloat('Detection', 'process_interval', fallback=1.0)
        self.save_interval_high_prob = self.config.getfloat('Detection', 'save_interval_high_prob', fallback=30.0)
        self.fixed_size = (self.config.getint('Detection', 'fixed_image_width', fallback=128),
                           self.config.getint('Detection', 'fixed_image_height', fallback=128))
        self.smoke_threshold = self.config.getfloat('Detection', 'smoke_threshold', fallback=0.5)
        # 注意: config 中的 min_event_duration 用於計數和郵件觸發
        self.min_event_duration = self.config.getfloat('Detection', 'min_event_duration', fallback=5.0) # 預設 5 分鐘
        logger.info(f"{self.side} 攝影機: min_event_duration 已設定為 {self.min_event_duration} 秒。") # <-- 添加這行

        # 新增：載入用於長條圖 +1 邏輯的參數
        self.high_prob_threshold_for_daily_count = self.config.getfloat('Detection', 'high_prob_threshold_for_daily_count', fallback=0.95)
        self.daily_count_interval_seconds = self.config.getfloat('Detection', 'daily_count_interval_minutes', fallback=5.0) * 60 # 轉換為秒

        # 獲取特徵組態子集
        self.feature_config = self.config['Features']
        # 獲取郵件組態子集
        self.email_config = self.config['EmailSettings']
        # 獲取分類器組態子集
        self.classifier_config = self.config['Classifier']

        # 獲取通用組態值
        self.sun_times_csv_path = self.config.get('General', 'sun_times_csv', fallback='sun_date.csv')
        self.default_sunrise = self.config.get('General', 'default_sunrise', fallback='06:00')
        self.default_sunset = self.config.get('General', 'default_sunset', fallback='19:00')
        self.sun_times_data = _read_sun_times_from_csv(self.sun_times_csv_path)
        self.max_reconnect_attempts = self.config.getint('General', 'watchdog_restart_timeout', fallback=60) // 10 # 簡單映射，粗略估計
        self.watchdog_interval = self.config.getint('General', 'watchdog_interval', fallback=30)

        self.last_frame_time = time.time()
        
        # 事件計數和郵件觸發專用 (載入狀態)
        self.smoke_event_start = state.get("smoke_event_start") # 機率首次超過 *基本* 閾值 (>= smoke_threshold) 的時間戳
        # 這兩個新的用於重複計數的變數
        self.last_counted_time = state.get("last_counted_time", 0) # 上次為重複計數而 +1 的時間戳
        self.initial_count_done = state.get("initial_count_done", False) # 標誌，確保第一次 +1 發生


        # 當前事件追蹤，用於持續時間和嚴重性
        self.current_event_start = state.get("current_event_start") # 當前潛在事件的第一個影格的時間戳
        self.current_event_severity = state.get("current_event_severity", 0.0) # 當前潛在事件的累積嚴重性

        self.cap_lock = threading.Lock()
        self.cap = None
        self.last_frame = None
        self.frame_lock = threading.Lock()
        self.reconnect_attempts = 0
        self.reconnect_cooldown = 1
        self.active = True
        self.last_detection_status = None # 追蹤偵測時間的變化
        self.detection_enabled = self.is_detection_time() # 初始檢查

        # 載入處理時間、影像儲存和郵件的狀態
        self.last_update = state.get("last_update", 0)
        self.high_prob_last_save_time = state.get("high_prob_last_save_time", 0)
        self.last_email_time = state.get("last_email_time", 0) # 載入郵件狀態
        self.min_email_interval = self.email_config.getfloat('min_email_interval', fallback=600.0)

        # 載入圖表和已完成事件的狀態 (確保正確的資料型別)
        state = state if state is not None else {}
        self.smoke_probs_history = deque(state.get("smoke_probs_history", []), maxlen=60) # 最多 60 個點的歷史紀錄
        # daily_counts 和 heatmap 鍵以字串形式儲存，載入後還原為 OrderedDict
        self.daily_counts = OrderedDict(state.get("daily_counts", {}))
        # heatmap 值可能以列表形式載入，轉換回 numpy 陣列
        heatmap_items = state.get("last7_heatmap", {}).items()
        self.last7_heatmap = OrderedDict(((str(k), np.array(v) if isinstance(v, list) else v) for k, v in heatmap_items))

        self.event_durations = state.get("event_durations", []) # 已完成事件的持續時間
        self.event_severities = state.get("event_severities", []) # 已完成事件的嚴重性

        # 熱力圖追蹤 (暫時性，不儲存在狀態中)
        self.heatmap_tracking_date = None # 追蹤上次熱力圖更新 bin 的日期
        self.last_half_hour_index = None # 追蹤上次熱力圖更新 bin 的半小時索引
        self.heatmap_last_update_time = time.time() # **修正：初始化 heatmap_last_update_time**

        # UI 元素
        self.video_canvas = tk.Canvas(self, width=768, height=432, bg='light green', highlightthickness=0)
        self.video_canvas.pack(pady=10)
        self.data_frame = tk.Frame(self, bg='light green')
        self.data_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 設定 Tkinter 元件的字體
        self.canvas_text_font = tkFont.Font(family=CHINESE_FONT, size=30)
        self.error_text_font = tkFont.Font(family=CHINESE_FONT, size=20)
        self.system_error_font = tkFont.Font(family=CHINESE_FONT, size=30)
        self.roi_status_font_scale = min(768, 432) / 1500.0 # ROI 文字的字體縮放比例
        self.overall_status_font_scale = min(768, 432) / 800.0 # 整體狀態文字的字體縮放比例



        # 將 heatmap_cbar 初始化為 None
        self.heatmap_cbar = None

        self._init_charts() # 初始化圖表

        # 僅在目前處於偵測時間且 ROI 存在時初始化 RTSP 串流
        if self.detection_enabled and self.rois:
            self.setup_stream()
        else:
             logger.warning(f"{self.side} 攝影機: 未設定串流 - 偵測已禁用 ({not self.detection_enabled}) 或未定義 ROI ({not self.rois})。")

        self.video_thread = threading.Thread(target=self.video_capture_thread, daemon=True)
        self.video_thread.start()

        # 啟動看門狗
        self.watchdog_time = time.time() # 看門狗上次檢查的時間
        # 重新安排第一次看門狗檢查
        self.after(self.watchdog_interval * 1000, self.watchdog_check)

        self.update_frame() # 啟動影格處理/顯示迴圈

    def set_classifier(self, clf):
        """為此面板設定已訓練的分類器。"""
        self.clf = clf
        if clf is not None:
             logger.info(f"{self.side} 攝影機: 分類器已設定。")
        else:
             logger.warning(f"{self.side} 攝影機: 分類器正被設定為 None。")

    def setup_stream(self):
        """初始化或嘗試重新連接視訊串流。"""
        # 僅當偵測啟用且 ROI 已定義時才嘗試設定 (偵測需要有意義的 ROI)
        if not self.detection_enabled:
             logger.debug(f"{self.side} 攝影機: setup_stream 被呼叫但偵測已禁用。")
             return
        if not self.rois:
             logger.warning(f"{self.side} 攝影機: 無法設定串流，未定義 ROI。")
             return

        with self.cap_lock:
            # 安全釋放任何現有的擷取物件
            if self.cap is not None:
                try:
                    # 確保釋放不會因串流不良而掛起
                    release_thread = threading.Thread(target=self.cap.release)
                    release_thread.start()
                    release_thread.join(timeout=5) # 最多等待 5 秒鐘以釋放
                    if release_thread.is_alive():
                         logger.warning(f"{self.side} 攝影機: cap.release 超時。資源可能仍被佔用。")
                         # 在某些情況下，可能需要終止進程或使用更激進的技術 (複雜)
                    else:
                         logger.info(f"{self.side} 攝影機: 在設定前已釋放現有串流。")
                except Exception as e:
                     logger.error(f"{self.side} 攝影機: 嘗試釋放現有串流時發生錯誤: {e}", exc_info=True)
                self.cap = None # 確保在嘗試釋放後為 None

            try:
                logger.info(f"{self.side} 攝影機: 嘗試開啟串流: {self.video_path}")
                self.cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
                # 設定緩衝區大小為 1 以降低延遲 (對 RTSP 通常很有幫助)
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 檢查串流是否成功開啟
                if not self.cap.isOpened():
                    logger.error(f"{self.side} 攝影機: 無法開啟串流 {self.video_path}")
                    # 向呼叫邏輯/看門狗指示失敗
                    raise IOError(f"無法開啟視訊串流 {self.video_path}")

                # 如果成功，重置重新連接嘗試次數和冷卻時間
                self.reconnect_attempts = 0
                self.reconnect_cooldown = 1 # 成功連接後重置冷卻時間
                logger.info(f"{self.side} 攝影機: 串流設定成功")

            except Exception as e:
                # 處理設定期間的錯誤
                logger.error(f"{self.side} 攝影機: 串流設定錯誤: {traceback.format_exc()}", exc_info=True)
                self.reconnect_attempts += 1
                if self.reconnect_attempts <= self.max_reconnect_attempts:
                     logger.warning(f"{self.side} 攝影機: 將在 {self.reconnect_cooldown} 秒後重新連接 (嘗試 {self.reconnect_attempts}/{self.max_reconnect_attempts})")
                     # 在 UI 執行緒中安排下一次設定嘗試
                     self.after(int(self.reconnect_cooldown * 1000), self.setup_stream)
                     # 冷卻時間呈指數退避，上限為 30 秒
                     self.reconnect_cooldown = min(30, self.reconnect_cooldown * 2)
                else:
                     # 達到最大嘗試次數，記錄嚴重錯誤。看門狗可能會觸發應用程式重新啟動。
                     logger.critical(f"{self.side} 攝影機: 串流設定已達到最大重新連接嘗試次數。串流是否永久失敗？")
                     # Also try to trigger application restart through the main UI
                     root = self.winfo_toplevel()
                     if hasattr(root, "restart_application"):
                         logger.critical(f"{self.side} 攝影機: 觸發應用程式重新啟動。")
                         root.after(1, root.restart_application) # Schedule restart on main UI thread

    def pause_stream(self):
        """釋放視訊擷取資源。"""
        with self.cap_lock:
            if self.cap is not None:
                try:
                    # 使用執行緒釋放，因為它有時可能會阻塞
                    release_thread = threading.Thread(target=self.cap.release)
                    release_thread.start()
                    release_thread.join(timeout=5) # 最多等待 5 秒
                    if release_thread.is_alive():
                         logger.warning(f"{self.side} 攝影機: cap.release 在暫停期間超時。資源可能仍被佔用。")
                    else:
                         logger.info(f"{self.side} 攝影機: 串流已暫停 (資源已釋放)")
                except Exception as e:
                    logger.error(f"{self.side} 暫停串流錯誤: {traceback.format_exc()}", exc_info=True)
                finally:
                    self.cap = None # 無論釋放成功/失敗，確保 cap 為 None
            else:
                 logger.debug(f"{self.side} 攝影機: 串流已暫停或未初始化。")

    def video_capture_thread(self):
        """執行緒，用於持續從視訊源擷取影格。"""
        last_frame_read_time = 0
        read_interval = 1.0 / 30.0 # 目標約 30fps

        while self.active:
            try:
                current_time = time.time()
                # 控制影格讀取速率
                if current_time - last_frame_read_time < read_interval:
                    time.sleep(max(0, read_interval - (current_time - last_frame_read_time)))
                    continue

                # 僅當串流預期處於活動狀態 (偵測啟用且 cap 存在) 時才嘗試讀取
                if not self.detection_enabled or self.cap is None or not self.cap.isOpened():
                    #logger.debug(f"{self.side} 擷取執行緒: 串流未啟用。正在休眠。") # 訊息過於詳細
                    time.sleep(0.1) # 如果未啟用或串流未開啟，則短暫休眠
                    continue

                with self.cap_lock:
                    try:
                        # 讀取影格 - 這是 RTSP 的阻塞呼叫
                        ret, frame = self.cap.read()
                    except cv2.error as e:
                        # 處理讀取期間潛在的 OpenCV 錯誤
                        logger.error(f"{self.side} 擷取執行緒 cv2.error 讀取影格: {e}。正在嘗試串流設定。")
                        # Invalidate cap to ensure setup_stream attempts reconnect
                        self.cap = None 
                        # In UI thread: self.after(1, self.setup_stream)
                        # The watchdog will also pick this up if no frame is read.
                        continue # 跳過處理此失敗的讀取

                if ret and frame is not None:
                    last_frame_read_time = time.time()
                    with self.frame_lock:
                        self.last_frame = frame.copy()
                        self.last_frame_time = last_frame_read_time # 成功讀取影格的時間戳
                    # 看門狗時間在處理發生時在 update_frame 中更新
                    # (此處移除看門狗時間更新，僅依賴 update_frame 處理)
                else:
                    # If ret is False or frame is None, there's a problem
                    # The watchdog will handle continuous reconnects based on self.last_frame_time
                    logger.debug(f"{self.side} 擷取執行緒: 讀取影格失敗 (ret={ret}, frame 為 None)。")
                    time.sleep(0.5) # Prevent tight loop on immediate read failure

            except Exception as e:
                logger.error(f"{self.side} 擷取執行緒未處理的錯誤: {traceback.format_exc()}", exc_info=True)
                # 引入休眠以防止崩潰迴圈
                time.sleep(1.0)

        logger.info(f"{self.side} 擷取執行緒停止。")

    def is_detection_time(self):
        """檢查當前時間是否在配置的偵測期間內。"""
        now = datetime.datetime.now().time()
        today_date = datetime.date.today()
        today_date_str = format_date(today_date)

        sunrise = None
        sunset = None

        # 首先嘗試從 CSV 資料讀取
        if today_date_str in self.sun_times_data:
            sunrise, sunset = self.sun_times_data[today_date_str]
            #logger.debug(f"{self.side}: 正在使用來自 CSV 的日出日落時間: {sunrise} - {sunset}，適用於 {today_date_str}")
        else:
            # 回退到組態中的預設時間
            try:
                sunrise = datetime.datetime.strptime(self.default_sunrise, '%H:%M').time()
                sunset = datetime.datetime.strptime(self.default_sunset, '%H:%M').time()
                #logger.debug(f"{self.side}: 正在使用來自組態的預設日出日落時間: {sunrise} - {sunset}，適用於 {today_date_str}")
            except ValueError as ve:
                logger.error(f"{self.side}: 組態中預設日出/日落格式無效 '{self.default_sunrise}' 或 '{self.default_sunset}'。假設為 24/7 偵測。錯誤: {ve}", exc_info=True)
                return True # 如果組態時間無效，則為 24/7 偵測

        # 檢查當前時間是否在範圍內
        if sunrise is not None and sunset is not None:
             if sunrise <= sunset:
                 return sunrise <= now <= sunset
             else: # 日落時間在午夜之後 (例如，從 22:00 到 04:00 偵測)
                 return now >= sunrise or now <= sunset
        else:
             # 如果預設時間有效，則不應達到此情況
             logger.warning(f"{self.side}: 無法確定日出日落時間 (CSV 或預設)。假設為 24/7 偵測。")
             return True # 安全網

    def watchdog_check(self):
        """檢查視訊執行緒是否處於活動狀態並接收影格。"""
        current_time = time.time()
        max_frame_staleness = self.config.getint('General', 'watchdog_restart_timeout', fallback=60)

        # 僅當偵測目前啟用時才檢查串流狀態
        if self.detection_enabled:
            # 檢查影格是否太舊 (串流可能卡住)
            if current_time - self.last_frame_time > max_frame_staleness:
                logger.warning(f"{self.side} 看門狗: 在 >{max_frame_staleness}s 內未收到新影格 ({current_time - self.last_frame_time:.1f}s 過期)。正在嘗試串流恢復。")
                # Invalidate cap to ensure setup_stream attempts reconnect
                with self.cap_lock:
                    if self.cap:
                        self.cap.release()
                        self.cap = None
                # Schedule setup_stream on the UI thread
                self.after(1, self.setup_stream)
                self.watchdog_time = current_time # Reset watchdog timer after attempt
            # 檢查擷取執行緒是否仍處於活動狀態 (它可能已崩潰)
            elif not self.video_thread.is_alive():
                 logger.critical(f"{self.side} 看門狗: 視訊擷取執行緒未啟用！正在嘗試串流設定並記錄崩潰詳細資訊。")
                 # Invalidate cap to ensure setup_stream attempts reconnect
                 with self.cap_lock:
                     if self.cap:
                         self.cap.release()
                         self.cap = None
                 self.after(1, self.setup_stream) # Schedule setup_stream on the UI thread
                 self.watchdog_time = current_time # Reset watchdog timer
            else:
                 self.watchdog_time = current_time # If check passes, update watchdog time

        # 重新安排下一次看門狗檢查
        if self.winfo_exists(): # 僅當小部件仍存在時才重新安排
             self.after(self.watchdog_interval * 1000, self.watchdog_check)
        else:
             logger.info(f"{self.side} 看門狗停止，面板不存在。")

    def _init_charts(self):
        """初始化 Matplotlib 圖表。"""
        try:
            plt.style.use('ggplot') # 使用 ggplot 風格，這個風格通常比較美觀
            # 曲線圖
            self.curve_fig = Figure(figsize=(7.5, 1.6), dpi=90)
            self.curve_ax = self.curve_fig.add_subplot(111)
            # 標題和軸標籤將在 update_curve_plot 中設定
            self.curve_line, = self.curve_ax.plot([], [], 'b-') # 初始化一個空線
            self.curve_canvas_tk = FigureCanvasTkAgg(self.curve_fig, master=self.data_frame)
            self.curve_canvas_tk.get_tk_widget().pack(pady=5, fill=tk.BOTH, expand=True)

            # 熱力圖
            self.heatmap_fig = Figure(figsize=(7.5, 1.8), dpi=90)
            self.heatmap_ax = self.heatmap_fig.add_subplot(111)
            self.heatmap_fig.subplots_adjust(left=0.08, right=0.95, top=0.85, bottom=0.15)
            # 標題和軸標籤將在 update_heatmap_display 中設定
            self.heatmap_canvas_tk = FigureCanvasTkAgg(self.heatmap_fig, master=self.data_frame)
            self.heatmap_canvas_tk.get_tk_widget().pack(pady=1, fill=tk.BOTH, expand=True)
            self.heatmap_im = None # 將在 update_heatmap_display 中賦值

            # 長條圖
            self.bar_fig = Figure(figsize=(7.5, 1.6), dpi=90)
            self.bar_ax = self.bar_fig.add_subplot(111)
            # 標題和軸標籤將在 update_bar_chart 中設定
            self.bar_canvas_tk = FigureCanvasTkAgg(self.bar_fig, master=self.data_frame)
            self.bar_canvas_tk.get_tk_widget().pack(pady=5, fill=tk.BOTH, expand=True)

            # 執行初始圖表更新 (這會第一次調用 update_heatmap_display)
            self.update_charts()
        except Exception as e:
             logger.error(f"{self.side} 攝影機: 初始化圖表失敗: {traceback.format_exc()}", exc_info=True)

    def update_frame(self):
        """處理影格和更新 UI 的主迴圈。"""
        # 在繼續之前檢查 UI 元素是否仍然存在
        if not self.winfo_exists():
            logger.info(f"{self.side} 面板不再存在，停止更新")
            self.cleanup() # 確保呼叫清理
            return

        try:
            now_dt = datetime.datetime.now()
            current_time_sec = now_dt.timestamp() # 使用時間戳計算事件持續時間
            current_detection_enabled = self.is_detection_time()

            # --- 處理偵測時間轉換 ---
            if current_detection_enabled != self.detection_enabled:
                self.detection_enabled = current_detection_enabled
                logger.info(f"{self.side}: 偵測狀態變更為 {self.detection_enabled}")
                if self.detection_enabled:
                    if self.rois:
                         self.setup_stream()
                    else:
                         logger.warning(f"{self.side} 攝影機: 偵測已啟用但無 ROI，未設定串流。")
                else:
                    self.pause_stream()
                    # 清除畫布並在偵測關閉時顯示訊息
                    self.video_canvas.delete("all")
                    self.video_canvas.create_text(768//2, 432//2, text="非偵測期間",
                                              font=self.canvas_text_font, fill="red") # 使用預設字體物件
                    self.after(1000, self.update_frame) # 安排下一次檢查
                    return # 退出當前更新週期

            # --- 如果偵測關閉，只需更新訊息並重新安排 ---
            if not self.detection_enabled:
                 self.after(1000, self.update_frame)
                 return

            # --- 如果偵測啟用 ---
            frame = None
            with self.frame_lock:
                if self.last_frame is not None:
                    frame = self.last_frame.copy()
                # 根據影格擷取時間計算過期時間
                frame_staleness = current_time_sec - self.last_frame_time if self.last_frame is not None else float('inf')

            if frame is None or frame_staleness > 5: # 如果影格為 None 或超過 5 秒
                 # 如果影格為 None 或太舊，顯示 "無視訊訊號"
                 # 看門狗將處理串流重新連接 (如果需要)
                 self.video_canvas.delete("all")
                 self.video_canvas.create_text(768//2, 432//2, text="無視訊訊號",
                                               font=self.canvas_text_font, fill="red") # 使用預設字體物件
                 # 正在等待影格時，安排下一次 UI 更新更快
                 self.after(100, self.update_frame) # 更頻繁地檢查影格
                 return # 如果沒有影格，則無需執行其他操作

            # --- 執行偵測並更新狀態 ---
            # 僅在配置的間隔內處理影格進行偵測
            if current_time_sec - self.last_update >= self.process_interval:
                self.last_update = current_time_sec
                self.watchdog_time = current_time_sec

                overall_label = "無煙霧" # 先給定預設值
                new_preds = [(0, 0.0)] * len(self.rois) if self.rois else [] # 先給定預設值
                current_max_smoke_prob = 0.0 # 先給定預設值

                # 判斷是否啟用高機率計數 (無論是否有分類器/ROI，都應初始化此變數)
                is_high_prob_smoke_now = False # <--- 確保這行在這裡，在 if self.clf 判斷之外

                # 在預測前檢查分類器是否可用且 ROI 已定義
                if self.clf is not None and self.rois:
                    overall_label, new_preds = predict_image_multiple_rois(
                        frame, self.rois, self.clf, self.feature_config, self.config['Detection'], self.classifier_config
                    )
                    self.last_predictions = new_preds # 儲存預測以供繪製

                    smoke_roi_probs = [prob for (pred, prob) in new_preds if pred == 1]
                    current_max_smoke_prob = max(smoke_roi_probs) if smoke_roi_probs else 0.0
                    logger.debug(f"{self.side} 攝影機: 處理影格。最大煙霧機率: {current_max_smoke_prob:.2f}")

                    # 判斷是否達到高機率計數閾值
                    is_high_prob_smoke_now = current_max_smoke_prob >= self.high_prob_threshold_for_daily_count
                else:
                    logger.debug(f"{self.side} 攝影機: 分類器或 ROI 不可用。已跳過此間隔的偵測處理。")
                    # overall_label, new_preds, current_max_smoke_prob, is_high_prob_smoke_now 已經在前面初始化

                # 現在，計算原始的 is_smoke_now，用於熱力圖和曲線圖等
                # 這個判斷應基於 'smoke_threshold'
                is_smoke_now = current_max_smoke_prob >= self.smoke_threshold # <--- **確保這行在這裡！** 
                                                                             #      它必須在 if self.clf ... else ... 塊之後，
                                                                             #      因為 current_max_smoke_prob 是在該塊中計算的。

                # --- 更新歷史和事件追蹤 ---
                # 將最大機率 (即使為 0.0) 追加到歷史紀錄
                self.smoke_probs_history.append((current_time_sec, current_max_smoke_prob))

                # 事件計數和郵件觸發邏輯
                today_str = now_dt.strftime("%Y-%m-%d")

                # 新的判斷條件：事件是否處於“高機率煙霧”狀態 (用於每日計數)
                if is_high_prob_smoke_now:
                    # 如果這是高機率煙霧事件的開始
                    if self.smoke_event_start is None:
                        logger.debug(f"{self.side} 攝影機: 高機率煙霧事件於 {datetime.datetime.fromtimestamp(current_time_sec).strftime('%Y-%m-%d %H:%M:%S')} 開始。")
                        self.smoke_event_start = current_time_sec
                        self.last_counted_time = current_time_sec # 初始化上次計數時間為事件開始時間
                        self.initial_count_done = False # 重置初始計數標誌

                    # 追蹤當前事件的總持續時間 (從煙霧開始)
                    event_duration_since_start = current_time_sec - self.smoke_event_start

                    # 條件 1, 2, 3: 第一次達到最小持續時間和高機率閾值
                    if (event_duration_since_start >= self.min_event_duration) and (not self.initial_count_done):
                        # 這是這個連續高機率事件首次超過最小持續時間
                        logger.info(f"{self.side} 攝影機: 高機率煙霧事件持續時間達到 {self.min_event_duration}s。事件於 {datetime.datetime.fromtimestamp(self.smoke_event_start).strftime('%Y-%m-%d %H:%M:%S')} 開始，觸發首次計數。")

                        # 增加每日計數
                        if today_str not in self.daily_counts:
                            self.daily_counts[today_str] = 0
                        self.daily_counts[today_str] += 1
                        self.initial_count_done = True # 標記為已完成首次計數
                        self.last_counted_time = current_time_sec # 更新上次計數時間
                        logger.info(f"{self.side} 攝影機: 每日煙霧事件計數已增加至 {self.daily_counts[today_str]} (首次計數)。")

                        # 觸發郵件通知 (此處邏輯不變，仍基於第一次滿足持續時間)
                        if self.email_config.getboolean('send_email_enabled', False):
                            if current_time_sec - self.last_email_time >= self.min_email_interval:
                                logger.info(f"{self.side} 攝影機: 符合最小事件持續時間且符合最小郵件間隔。正在觸發郵件。")
                                email_thread = threading.Thread(
                                    target=send_smoke_email,
                                    args=(
                                        self.email_config,
                                        self.side,
                                        self.smoke_event_start,
                                        event_duration_since_start,
                                        current_max_smoke_prob # 使用當前的最大機率作為嚴重性
                                    ),
                                    daemon=True
                                )
                                email_thread.start()
                                self.last_email_time = current_time_sec
                            else:
                                logger.debug(f"{self.side} 攝影機: 符合事件持續時間，但未達到最小郵件間隔。")

                    # 條件 4: 同一事件每 N 分鐘重複計數
                    elif self.initial_count_done and (current_time_sec - self.last_counted_time >= self.daily_count_interval_seconds):
                        logger.info(f"{self.side} 攝影機: 高機率煙霧事件持續中，已達到 {self.daily_count_interval_seconds/60:.0f} 分鐘間隔，再次計數。")
                        if today_str not in self.daily_counts: # 應已存在，但作防禦性檢查
                            self.daily_counts[today_str] = 0
                        self.daily_counts[today_str] += 1
                        self.last_counted_time = current_time_sec # 更新上次計數時間
                        logger.info(f"{self.side} 攝影機: 每日煙霧事件計數已增加至 {self.daily_counts[today_str]} (重複計數)。")
                        # 注意: 重複計數不觸發重複郵件，郵件邏輯保留在首次觸發處。

                    # 這段邏輯用於追蹤整體事件的持續時間和嚴重性，與長條圖計數是分開的。
                    # 如果您希望 'current_event_severity' 也只針對高機率煙霧來計算，這裡也需要修改
                    if self.current_event_start is None:
                        self.current_event_start = current_time_sec
                        self.current_event_severity = current_max_smoke_prob
                    else:
                        duration_since_current_event_start = current_time_sec - self.current_event_start
                        if duration_since_current_event_start > 0:
                            self.current_event_severity = (self.current_event_severity * (duration_since_current_event_start - self.process_interval) + current_max_smoke_prob * self.process_interval) / duration_since_current_event_start
                            if self.current_event_severity < 0 or np.isnan(self.current_event_severity):
                                self.current_event_severity = current_max_smoke_prob
                        else:
                            self.current_event_severity = current_max_smoke_prob


                else: # 機率降至 high_prob_threshold_for_daily_count 以下 (高機率事件結束)
                    # 如果有高機率事件正在進行，則標記為結束
                    if self.smoke_event_start is not None:
                         # 檢查 *已完成* 的事件是否符合持續時間閾值 (針對整體事件數據，非每日計數)
                         event_duration_actual = current_time_sec - self.smoke_event_start
                         if event_duration_actual >= self.min_event_duration: # 這裡仍使用 min_event_duration 來判斷事件的完整性
                             if self.current_event_start is not None:
                                  final_event_duration = current_time_sec - self.current_event_start
                                  final_avg_severity = self.current_event_severity
                                  self.event_durations.append(final_event_duration)
                                  self.event_severities.append(final_avg_severity)
                                  logger.info(f"{self.side} 攝影機: 煙霧事件結束。持續時間: {final_event_duration:.1f}s，最終平均嚴重性: {final_avg_severity:.2f}")
                             else:
                                logger.warning(f"{self.side} 攝影機: 事件結束但 current_event_start 為 None。")
                         else:
                              logger.debug(f"{self.side} 攝影機: 潛在事件太短 ({event_duration_actual:.1f}s)，未計數。")


                    # 無論是否計數，重置事件追蹤變數
                    self.smoke_event_start = None
                    self.last_counted_time = 0 # 重置重複計數時間
                    self.initial_count_done = False # 重置初始計數標誌
                    self.current_event_start = None
                    self.current_event_severity = 0.0

                # 熱力圖累積邏輯
                # 確保當前日期在熱力圖資料結構中
                today_heatmap_str = now_dt.strftime("%Y-%m-%d")
                if today_heatmap_str not in self.last7_heatmap:
                    # 使用包含 48 個半小時 bin 的零陣列初始化日期
                    self.last7_heatmap[today_heatmap_str] = np.zeros(48, dtype=int) # 使用 int 計數

                # 計算當前半小時索引 (0 到 47)
                half_hour_idx = (now_dt.hour * 60 + now_dt.minute) // 30

                # 僅在達到閾值時才累加計數
                if is_smoke_now: # 使用基於閾值的布林標誌 (這個是基於 smoke_threshold 的)
                    # 增加當前半小時 bin 的計數
                    if 0 <= half_hour_idx < 48: # Ensure index is valid
                        self.last7_heatmap[today_heatmap_str][half_hour_idx] += 1
                    else:
                        logger.warning(f"{self.side} 攝影機: 熱力圖半小時索引 {half_hour_idx} 超出範圍 [0, 47]。跳過熱力圖更新。")

                # 在熱力圖資料中僅保留最近 7 天 (按日期字串排序)
                # 排序鍵以確保在移除最舊鍵之前順序一致
                sorted_heatmap_dates = sorted(self.last7_heatmap.keys())
                while len(sorted_heatmap_dates) > 7:
                     oldest_heatmap_date = sorted_heatmap_dates.pop(0) # 獲取最舊的日期字串
                     if oldest_heatmap_date in self.last7_heatmap:
                         del self.last7_heatmap[oldest_heatmap_date] # 從 OrderedDict 中移除
                         logger.debug(f"{self.side}: 移除最舊的熱力圖資料 {oldest_heatmap_date}。")

                # 在每日計數中僅保留最近 7 天
                sorted_daily_dates = sorted(self.daily_counts.keys())
                while len(sorted_daily_dates) > 7:
                    oldest_daily_date = sorted_daily_dates.pop(0)
                    if oldest_daily_date in self.daily_counts:
                        del self.daily_counts[oldest_daily_date]
                        logger.debug(f"{self.side}: 移除最舊的每日計數 {oldest_daily_date}。")
                
                # 定期更新圖表 (例如，每 3 秒)
                # 判斷是否需要更新圖表：每 3 秒一次，或者如果上次更新時間超過 5 秒
                if current_time_sec // 3 != (current_time_sec - self.process_interval) // 3 or \
                   (current_time_sec - self.heatmap_last_update_time) > 5:
                    logger.debug(f"{self.side} 攝影機: 觸發圖表更新。")
                    self.update_charts()
                    self.heatmap_last_update_time = current_time_sec

                # 儲存高機率影像
                if current_max_smoke_prob > 0.9: # 儲存影像的閾值
                    if current_time_sec - self.high_prob_last_save_time >= self.save_interval_high_prob:
                        try:
                            # 確保影像儲存目錄存在
                            os.makedirs(self.image_save_dir, exist_ok=True)
                            filename = time.strftime("%Y%m%d_%H%M%S.jpg", time.localtime())
                            save_path = os.path.join(self.image_save_dir, filename)
                            
                            # 在影像上繪製文字時使用Pillow來處理中文
                            # 將OpenCV BGR影像轉換為PIL RGB影像
                            frame_to_save_rgb = cv2.cvtColor(frame.copy(), cv2.COLOR_BGR2RGB)
                            pil_img = Image.fromarray(frame_to_save_rgb)
                            draw = ImageDraw.Draw(pil_img)

                            # 加載中文字體
                            if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
                                try:
                                    # 根據影像大小調整字體大小，使其在存儲的圖片中清晰
                                    font_size = int(self.overall_status_font_scale * 50) # 乘以一個係數使字體大一點
                                    chinese_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, font_size)
                                except IOError:
                                    logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                                    chinese_font_pil = ImageFont.load_default()
                            else:
                                chinese_font_pil = ImageFont.load_default()

                            # 繪製整體狀態文字
                            # overall_smoke_detected 是基於 pred == 1 的結果，而 pred == 1 是基於 smoke_threshold
                            overall_smoke_detected = any(pred == 1 for (pred, _) in new_preds)
                            smoke_probs = [prob for (pred, prob) in new_preds if pred == 1]
                            overall_max_prob = max(smoke_probs) if smoke_probs else 0.0

                            # 警報顏色 (RGB格式，因為PIL是RGB)
                            overall_alert_color_rgb = (255, 0, 0) if overall_max_prob > 0.9 else (255, 165, 0) # PIL是RGB
                            if overall_smoke_detected:
                                status_text = f"煙霧警報 ({overall_max_prob:.2f})"
                                text_color_rgb = overall_alert_color_rgb
                            else:
                                status_text = "無煙霧"
                                text_color_rgb = (0, 255, 0) # 無煙霧為綠色
                            
                            # PIL繪製文本
                            draw.text((10, pil_img.height - font_size - 10), status_text, font=chinese_font_pil, fill=text_color_rgb)

                            # 繪製ROI及其狀態
                            for i, roi in enumerate(self.rois):
                                if i < len(new_preds):
                                    pred, prob = new_preds[i]
                                else:
                                    pred, prob = (0, 0.0)
                                
                                roi_color_rgb = (255, 0, 0) if pred == 1 else (0, 255, 0)

                                # 繪製多邊形 (PIL繪製多邊形需要列表形式的點)
                                # 將OpenCV格式的ROI點轉換為PIL繪圖所需的扁平化列表 (x1, y1, x2, y2, ...)
                                pil_roi_points = [coord for point in roi for coord in point]
                                draw.polygon(pil_roi_points, outline=roi_color_rgb, width=2)

                                # ROI文字
                                roi_status_text = f"{prob:.2f}"
                                roi_font_size = int(self.roi_status_font_scale * 50)
                                if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
                                    try:
                                        chinese_roi_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, roi_font_size)
                                    except IOError:
                                        chinese_roi_font_pil = ImageFont.load_default()
                                else:
                                    chinese_roi_font_pil = ImageFont.load_default()

                                x_min = np.min(np.array(roi)[:, 0])
                                y_min = np.min(np.array(roi)[:, 1])
                                # text_pos_roi = (x_min, y_min - roi_font_size - 5 if y_min > (roi_font_size + 5) else y_min + 5)
                                text_pos_roi = (x_min - 80, y_min + 90)
                                draw.text(text_pos_roi, roi_status_text, font=chinese_roi_font_pil, fill=roi_color_rgb)


                            # 將PIL影像轉換回OpenCV BGR格式以保存
                            frame_to_save = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

                            cv2.imwrite(save_path, frame_to_save)
                            logger.info(f"{self.side}: 已儲存高機率煙霧影像: {save_path}")
                            self.high_prob_last_save_time = current_time_sec # 使用 current_time_sec 更新儲存時間

                        except Exception as e:
                            logger.error(f"{self.side} 影像儲存錯誤: {traceback.format_exc()}", exc_info=True)

            # --- 繪製覆蓋層 (即使此影格未進行偵測處理) ---
            # 如果可用，使用上次計算的預測
            if frame is not None and hasattr(self, 'last_predictions') and self.last_predictions is not None and self.rois: # 修正: 檢查 last_predictions 是否存在
                 # Call the drawing function directly on the frame
                 overlayed_frame = self._draw_detection_overlay_with_chinese(frame.copy(), self.last_predictions, self.rois, self.smoke_threshold)
            else:
                 overlayed_frame = frame # 如果沒有預測或 ROI 或 ROI 為空，則使用原始影格
                 if overlayed_frame is not None:
                     # Add a text overlay indicating no ROIs defined or waiting for detection
                     status_text = "等待偵測資料..."
                     if not self.rois:
                         status_text = "未定義 ROI！"
                     # 將OpenCV BGR影像轉換為PIL RGB影像
                     pil_img = Image.fromarray(cv2.cvtColor(overlayed_frame, cv2.COLOR_BGR2RGB))
                     draw = ImageDraw.Draw(pil_img)
                     if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
                         try:
                             font_size = int(self.overall_status_font_scale * 50) # 乘以一個係數使字體大一點
                             chinese_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, font_size)
                         except IOError:
                             chinese_font_pil = ImageFont.load_default()
                     else:
                         chinese_font_pil = ImageFont.load_default()

                     draw.text((10, pil_img.height - font_size - 10), status_text, font=chinese_font_pil, fill=(0, 0, 255))
                     overlayed_frame = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
                     logger.debug(f"{self.side} 攝影機: 無有效 ROI 或預測資料可繪製。")


            # --- 在畫布中顯示影格 ---
            if overlayed_frame is not None:
                try:
                    # 轉換為 PIL 的 RGB 之前，確保影格為 BGR
                    if len(overlayed_frame.shape) == 2: # 如果是灰度圖，轉換為 BGR
                         frame_bgr = cv2.cvtColor(overlayed_frame, cv2.COLOR_GRAY2BGR)
                    elif overlayed_frame.shape[2] == 4: # 如果是 BGRA，轉換為 BGR
                         frame_bgr = cv2.cvtColor(overlayed_frame, cv2.COLOR_BGRA2BGR)
                    else: # 假設為 BGR
                         frame_bgr = overlayed_frame

                    frame_resized = cv2.resize(frame_bgr, (768, 432))
                    frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                    image_pil = Image.fromarray(frame_rgb)
                    photo = ImageTk.PhotoImage(image=image_pil)

                    self.video_canvas.delete("all")
                    self.video_canvas.photo = photo # 保留引用
                    self.video_canvas.create_image(0, 0, image=photo, anchor=tk.NW)
                except Exception as e:
                    logger.error(f"{self.side} 顯示影格錯誤: {traceback.format_exc()}", exc_info=True)
                    # 如果繪製失敗，在畫布上顯示錯誤訊息
                    self.video_canvas.delete("all")
                    self.video_canvas.create_text(768//2, 432//2, text=f"顯示錯誤:\n{e}",
                                                  font=self.error_text_font, fill="red") # 使用預設字體物件
            else:
                 # 如果此處影格為 None，則應已由 frame_staleness 檢查捕獲
                 # 但作為回退，顯示「無視訊訊號」
                 self.video_canvas.delete("all")
                 self.video_canvas.create_text(768//2, 432//2, text="無視訊訊號",
                                               font=self.canvas_text_font, fill="red") # 使用預設字體物件

        except Exception as e:
            logger.error(f"{self.side} 影格更新錯誤: {traceback.format_exc()}", exc_info=True)
            # 如果更新迴圈中發生重大錯誤，安排檢查以防止崩潰
            self.video_canvas.delete("all")
            self.video_canvas.create_text(768//2, 432//2, text=f"系統錯誤\n檢查日誌",
                                          font=self.system_error_font, fill="red") # 使用預設字體物件
            self.after(2000, self.update_frame) # 延遲後重試

        # 無論是否進行處理，都安排下一次影格更新
        # 目標是平滑的 UI 更新速率 (例如，20 fps = 50ms 間隔)
        self.after(50, self.update_frame) # 安排下一次更新

    def _draw_detection_overlay_with_chinese(self, frame, predictions, rois, smoke_threshold):
        """
        在影格上繪製 ROI 和預測結果，並支援中文字符。
        使用PIL來繪製文本，然後將PIL圖片轉換回OpenCV格式。
        """
        if frame is None:
            return frame
        if predictions is None or not rois or len(predictions) != len(rois):
            logger.warning(f"{self.side}: _draw_detection_overlay_with_chinese called with invalid predictions or ROIs.")
            # Draw a default status if no valid detection input can be processed
            # Convert frame to PIL image for drawing
            pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_img)
            
            # Load font for this message
            if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
                try:
                    font_size = int(self.overall_status_font_scale * 50) # 乘以一個係數使字體大一點
                    chinese_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, font_size)
                except IOError:
                    logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                    chinese_font_pil = ImageFont.load_default()
            else:
                chinese_font_pil = ImageFont.load_default()
            
            draw.text((10, pil_img.height - font_size - 10), "等待偵測資料...", font=chinese_font_pil, fill=(0, 0, 255))
            return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)


        # Convert frame to PIL image for drawing
        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)

        # 加載用於整體狀態文字的字體
        if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
            try:
                overall_font_size = int(self.overall_status_font_scale * 150) # Scale up for better visibility on original frame size
                chinese_overall_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, overall_font_size)
            except IOError:
                logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                chinese_overall_font_pil = ImageFont.load_default()
        else:
            chinese_overall_font_pil = ImageFont.load_default()

        # 決定整體狀態和顏色
        overall_smoke_detected = any(pred == 1 for (pred, _) in predictions)
        smoke_probs = [prob for (pred, prob) in predictions if pred == 1]
        overall_max_prob = max(smoke_probs) if smoke_probs else 0.0

        overall_alert_color_rgb = (255, 0, 0) if overall_max_prob > 0.9 else (255, 165, 0) # PIL使用RGB
        if overall_smoke_detected:
            status_text = f"煙霧警報 ({overall_max_prob:.2f})"
            text_color_rgb = overall_alert_color_rgb
        else:
            status_text = "無煙霧"
            text_color_rgb = (0, 255, 0) # 無煙霧為綠色

        # 繪製整體狀態文字
        # 計算文字位置 (PIL的text方法需要top-left corner)
        text_origin_x = 10
        #text_origin_y = pil_img.height - overall_font_size - 10 # 底部留白
        text_origin_y = 10 # 底部留白
        draw.text((text_origin_x, text_origin_y), status_text, font=chinese_overall_font_pil, fill=text_color_rgb)


        # 加載用於ROI狀態文字的字體
        if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
            try:
                roi_font_size = int(self.roi_status_font_scale * 150) # Scale up for better visibility
                chinese_roi_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, roi_font_size)
            except IOError:
                logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                chinese_roi_font_pil = ImageFont.load_default()
        else:
            chinese_roi_font_pil = ImageFont.load_default()

        # 繪製 ROI 和個別 ROI 狀態
        for i, roi in enumerate(rois):
            if i < len(predictions):
                pred, prob = predictions[i]
            else:
                pred, prob = (0, 0.0)

            roi_color_rgb = (255, 0, 0) if pred == 1 else (0, 255, 0) # PIL使用RGB

            # 繪製多邊形 (PIL繪製多邊形需要列表形式的點)
            pil_roi_points = [coord for point in roi for coord in point]
            draw.polygon(pil_roi_points, outline=roi_color_rgb, width=5)

            # 在 ROI 附近繪製狀態文字
            roi_status_text = f"{prob:.2f}"
            x_min = np.min(np.array(roi)[:, 0])
            y_min = np.min(np.array(roi)[:, 1])
            #text_pos_roi = (x_min, y_min - roi_font_size - 5 if y_min > (roi_font_size + 5) else y_min + 5)
            text_pos_roi = (x_min - 80, y_min + 90)
            draw.text(text_pos_roi, roi_status_text, font=chinese_roi_font_pil, fill=roi_color_rgb)

        # 將PIL影像轉換回OpenCV BGR格式
        return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

    def update_curve_plot(self):
        """更新即時機率曲線圖。"""
        try:
            self.curve_ax.clear()
            self.curve_ax.set_title('即時煙霧機率')
            self.curve_ax.set_xlabel('時間 (秒)')
            self.curve_ax.set_ylabel('煙霧機率')
            self.curve_ax.set_ylim(0, 1.1)
            self.curve_ax.axhline(self.smoke_threshold, color='r', linestyle='--', label=f'閾值 ({self.smoke_threshold:.2f})') # 添加閾值線

            if self.smoke_probs_history:
                # 確保歷史紀錄包含 (時間, 機率) 元組
                valid_history = [p for p in self.smoke_probs_history if isinstance(p, tuple) and len(p) == 2]
                if not valid_history:
                    logger.warning(f"{self.side} 曲線圖: 煙霧機率歷史紀錄為空或無效。")
                    self.curve_ax.legend(loc='upper right', fontsize=8)
                    self.curve_fig.tight_layout()
                    self.curve_canvas_tk.draw_idle()
                    return

                times, probs = zip(*valid_history)
                # 計算自歷史紀錄視窗開始的相對時間
                start_time = times[0]
                rel_times = [t - start_time for t in times]

                self.curve_ax.plot(rel_times, probs, 'b-', label='最大 ROI 機率')
                self.curve_ax.legend(loc='upper right', fontsize=8)

                # 為機率 >= 閾值的時段添加陰影區域
                # 找到機率 >= 閾值的連續區段
                event_segments = []
                segment_start_idx = None
                for i in range(len(probs)):
                     if probs[i] >= self.smoke_threshold:
                          if segment_start_idx is None:
                               segment_start_idx = i
                     else:
                          if segment_start_idx is not None:
                               event_segments.append((segment_start_idx, i))
                               segment_start_idx = None
                # 如果最後一個區段延伸到歷史紀錄末尾，則添加它
                if segment_start_idx is not None:
                     event_segments.append((segment_start_idx, len(probs)))

                # 繪製陰影區域
                for start_idx, end_idx in event_segments:
                     if start_idx < len(rel_times): # 確保索引有效
                          # 使用陰影的起點和終點的實際時間
                          start_time_shade = rel_times[start_idx]
                          # 結束時間應為區段中最後一個點的時間，如果是最後一個點，則加一個間隔
                          if end_idx < len(rel_times): # 區段在歷史紀錄內結束
                              end_time_shade = rel_times[end_idx-1] + self.process_interval if end_idx > 0 else rel_times[start_idx] + self.process_interval
                          else: # 區段延伸到歷史紀錄末尾
                              end_time_shade = rel_times[-1] + self.process_interval

                          # 繪製陰影區域
                          self.curve_ax.axvspan(start_time_shade, end_time_shade,
                                               color='red', alpha=0.2)

                # 根據歷史紀錄持續時間設定 x 軸限制
                if rel_times:
                    # 將 xlim 設定為從 0 到最後一個點的時間 + 緩衝區
                    x_max = rel_times[-1] if len(rel_times) > 1 else self.process_interval
                    self.curve_ax.set_xlim(0, x_max + self.process_interval) # 添加一點緩衝區

            # 隱藏預設 x 軸刻度標籤，因為它們不是即時的
            self.curve_ax.set_xticklabels([])

            self.curve_fig.tight_layout()
            self.curve_canvas_tk.draw_idle()

        except Exception as e:
            logger.error(f"{self.side} 曲線圖錯誤: {traceback.format_exc()}", exc_info=True)

    def update_heatmap_display(self):
        """更新顯示最近 7 天煙霧頻率的熱力圖。"""
        try:
            self.heatmap_ax.clear()
            self.heatmap_ax.set_title('熱力圖 (最近 7 天)', fontsize=10)
            self.heatmap_ax.set_xlabel('一天中的小時', fontsize=8)
            self.heatmap_ax.set_ylabel('日期', fontsize=8)

            # 確保我們有最近 7 天的資料以獲得一致的熱力圖大小
            today = datetime.date.today()
            # 獲取最近 7 天的日期，最早的在前
            dates = [(today - datetime.timedelta(days=i)) for i in range(6, -1, -1)]
            date_strs = [d.strftime("%Y-%m-%d") for d in dates] # 使用 YYYY-MM-DD 以保持內部鍵的一致性

            matrix = []
            for ds in date_strs:
                # 使用 YYYY-MM-DD 鍵檢索資料
                # 確保當天的資料是一個大小為 48 的 numpy 陣列
                day_data = self.last7_heatmap.get(ds, np.zeros(48, dtype=int))
                if isinstance(day_data, list): # 處理狀態以列表形式載入的情況
                     day_data = np.array(day_data, dtype=int)

                if isinstance(day_data, np.ndarray) and day_data.shape == (48,):
                     matrix.append(day_data)
                else:
                     logger.warning(f"{ds} 的熱力圖資料格式不符合預期 ({type(day_data)}, 形狀 {getattr(day_data, 'shape', 'N/A')})。正在重置此日期的資料。")
                     matrix.append(np.zeros(48, dtype=int))
                     # 更正儲存的資料格式 (對於儲存狀態很重要)
                     self.last7_heatmap[ds] = np.zeros(48, dtype=int) # 確保它是一個 numpy 陣列

            matrix = np.array(matrix, dtype=int)

            # 確保矩陣具有正確的維度 (7 天 x 48 個半小時)
            if matrix.shape != (7, 48):
                 logger.error(f"{self.side} 熱力圖矩陣形狀錯誤: {matrix.shape}。預期為 (7, 48)。無法顯示。", exc_info=True)
                 # 繪製錯誤佔位符
                 self.heatmap_ax.text(0.5, 0.5, "熱力圖資料錯誤", horizontalalignment='center', verticalalignment='center', transform=self.heatmap_ax.transAxes, color='red', fontdict={'family':CHINESE_FONT})
                 self.heatmap_fig.canvas.draw_idle()
                 return # 跳過繪製實際的熱力圖

            # 使用色彩映射 (例如，'YlOrRd' 或 'hot')
            cmap = plt.get_cmap('YlOrRd')

            # 設定顏色縮放的 vmax。
            # 如果數據都是 0，則 vmax 至少為 1 以顯示基礎顏色
            max_val_matrix = np.max(matrix) if np.any(matrix) else 1
            # 設置為資料的 95 百分位數或固定最大值，如果資料太分散
            fixed_vmax = 1800 # 設定熱力圖最大值為 1800
            actual_vmax = max(max_val_matrix, fixed_vmax)


            self.heatmap_im = self.heatmap_ax.imshow(matrix, cmap=cmap, aspect='auto', interpolation='nearest', vmin=0, vmax=actual_vmax)

            # 設定小時的 x 軸刻度 (0, 1, ..., 23)
            tick_positions = np.arange(0.5, 48.5, 2) # 將刻度定位在 bin 之間 (0.5 是為了讓 00 居中)
            tick_labels = [f"{i:02d}" for i in range(24)] # 小時標籤 (00, 01, ..., 23)
            self.heatmap_ax.set_xticks(tick_positions)
            self.heatmap_ax.set_xticklabels(tick_labels, fontsize=7) # 調整字體大小

            # 設定日期的 y 軸刻度 (最近 7 天)
            self.heatmap_ax.set_yticks(range(len(date_strs)))
            # 格式化日期以供顯示 (例如，7/28)
            date_labels_formatted = [format_date(d) for d in dates]
            self.heatmap_ax.set_yticklabels(date_labels_formatted, fontsize=7) # 調整字體大小

            # 添加顏色條 (只在第一次創建，之後只更新)
            if self.heatmap_cbar is None:
                # 僅在 heatmap_im 被正確初始化後才創建顏色條
                if self.heatmap_im:
                    self.heatmap_cbar = self.heatmap_fig.colorbar(self.heatmap_im, ax=self.heatmap_ax, orientation='vertical', fraction=0.02, pad=0.04, label=f'(最大 {actual_vmax})')
            else:
                # 更新現有的顏色條
                self.heatmap_cbar.update_normal(self.heatmap_im)
                self.heatmap_cbar.set_label(f'(最大 {actual_vmax})')


            self.heatmap_fig.tight_layout()
            self.heatmap_fig.canvas.draw_idle()

        except Exception as e:
            logger.error(f"{self.side} 熱力圖繪製錯誤: {traceback.format_exc()}", exc_info=True)

    def update_bar_chart(self):
        """更新顯示每日煙霧事件計數的長條圖。"""
        try:
            self.bar_ax.clear()
            self.bar_ax.set_title('每日煙霧事件 (最近 7 天)')
            self.bar_ax.set_ylabel("煙霧事件")

            # 確保我們有最近 7 天的資料
            today = datetime.date.today()
            # 獲取最近 7 天的日期，最早的在前
            dates = [(today - datetime.timedelta(days=i)) for i in range(6, -1, -1)]
            date_strs = [d.strftime("%Y-%m-%d") for d in dates] # 使用 YYYY-MM-DD 以保持內部鍵的一致性

            # 獲取最近 7 天的計數，如果日期缺失則使用 0
            counts = [self.daily_counts.get(ds, 0) for ds in date_strs]

            # 創建長條圖
            bars = self.bar_ax.bar(range(len(date_strs)), counts, color='skyblue')

            # 如果計數 > 0，則在長條圖頂部添加計數標籤
            for i, count in enumerate(counts):
                if count > 0:
                    self.bar_ax.text(i, count + 0.1, str(count), ha='center', va='bottom', fontsize=9)

            # 設定 x 軸刻度以顯示格式化日期
            self.bar_ax.set_xticks(range(len(date_strs)))
            x_labels = [format_date(d) for d in dates]
            self.bar_ax.set_xticklabels(x_labels, rotation=0, fontsize=8) # 如果標籤重疊則旋轉

            # 清理圖表外觀
            self.bar_ax.spines['top'].set_visible(False)
            self.bar_ax.spines['right'].set_visible(False)
            # 調整 y 軸限制，即使最大計數為 0，也要確保至少為 1
            self.bar_ax.set_ylim(0, max(counts) * 1.2 if max(counts) > 0 else 1)

            self.bar_fig.tight_layout()
            self.bar_canvas_tk.draw_idle()

        except Exception as e:
            logger.error(f"{self.side} 長條圖錯誤: {traceback.format_exc()}", exc_info=True)



    def update_charts(self):
        """呼叫所有圖表更新方法。"""
        try:
            self.update_curve_plot()
            self.update_heatmap_display()
            self.update_bar_chart()
            gc.collect() # 觸發垃圾回收
        except Exception as e:
            logger.error(f"{self.side} 整體圖表更新錯誤: {traceback.format_exc()}", exc_info=True)

    def get_state(self):
        """返回面板的當前狀態以供儲存。"""
        try:
            state = {}
            # 儲存時將 deque 轉換為列表
            state["smoke_probs_history"] = list(self.smoke_probs_history)
            # 儲存時將 OrderedDict 項轉換為 (鍵, 值) 元組的列表
            # np.savez_compressed 可以處理值為 numpy 陣列的元組列表
            state["daily_counts"] = list(self.daily_counts.items())
            state["last7_heatmap"] = [(k, v.tolist() if isinstance(v, np.ndarray) else v) for k, v in self.last7_heatmap.items()] # 確保 numpy 陣列轉換為列表

            state["event_durations"] = self.event_durations
            state["event_severities"] = self.event_severities
            state["current_event_start"] = self.current_event_start
            state["current_event_severity"] = self.current_event_severity
            state["smoke_event_start"] = self.smoke_event_start
            # 移除了 self.event_counted 的儲存
            state["last_counted_time"] = self.last_counted_time # 新增
            state["initial_count_done"] = self.initial_count_done # 新增

            # 儲存時間以恢復狀態
            state["last_update"] = self.last_update
            state["high_prob_last_save_time"] = self.high_prob_last_save_time
            state["last_email_time"] = self.last_email_time # 儲存郵件狀態

            return state
        except Exception as e:
            logger.error(f"{self.side} 獲取狀態錯誤: {traceback.format_exc()}", exc_info=True)
            return {} # 錯誤時返回空狀態

    def cleanup(self):
        """在面板關閉前清理資源。"""
        logger.info(f"{self.side}: 正在清理資源")
        self.active = False # 通知執行緒停止

        # 等待視訊執行緒結束
        if hasattr(self, "video_thread") and self.video_thread.is_alive():
            logger.info(f"{self.side}: 正在等待視訊執行緒...")
            try:
                # 給執行緒一點時間結束，如果需要則強制退出
                self.video_thread.join(timeout=2)
                if self.video_thread.is_alive():
                     logger.warning(f"{self.side}: 視訊執行緒在超時後未正常結束。")
            except Exception as e:
                logger.error(f"{self.side}: 加入視訊執行緒錯誤: {traceback.format_exc()}", exc_info=True)

        # 釋放 VideoCapture 物件
        self.pause_stream() # 使用 pause_stream 方法

        # 關閉 matplotlib 圖形
        try:
            # 關閉前檢查圖形物件是否存在且不為 None
            if hasattr(self, 'curve_fig') and self.curve_fig is not None:
                plt.close(self.curve_fig)
                self.curve_fig = None
            if hasattr(self, 'heatmap_fig') and self.heatmap_fig is not None:
                 plt.close(self.heatmap_fig)
                 self.heatmap_fig = None
            if hasattr(self, 'bar_fig') and self.bar_fig is not None:
                 plt.close(self.bar_fig)
                 self.bar_fig = None
        except Exception as e:
            logger.error(f"{self.side} 攝影機: 關閉 matplotlib 圖形錯誤: {traceback.format_exc()}", exc_info=True)

        gc.collect() # 觸發垃圾回收
        logger.info(f"{self.side}: 清理完成")

# ============================
# 7. 主視窗 SmokeDetectionUI 類別 (參數從 config 傳入)
# ============================
class SmokeDetectionUI(tk.Tk):
    def __init__(self, config, left_clf, right_clf):
        super().__init__()
        self.app_config = config # 儲存 config 物件, 修改了變數名稱
        self.left_clf = left_clf
        self.right_clf = right_clf

        self.title("煙霧偵測系統")
        self.geometry("1550x1000") # 目前為固定大小
        self.resizable(True, True) # 允許調整大小
        # 處理視窗關閉按鈕和 Escape 鍵
        self.protocol("WM_DELETE_WINDOW", self.on_exit)
        self.bind("<Escape>", lambda e: self.on_exit())

        self._setup_ui_config()

        # 從 config 獲取狀態檔案路徑
        self.state_file = self.app_config.get('General', 'state_file', fallback="smoke_state.npz") # 使用 self.app_config
        self.saved_state = self._load_state() # 初始化面板前載入狀態

        self._init_panels() # 載入狀態 *後* 初始化面板

        # 從 config 獲取自動儲存間隔？為穩健性保持固定的 5 分鐘
        self.auto_save_interval_ms = 300000 # 5 分鐘 (毫秒)
        # 安排第一次自動儲存
        if self.winfo_exists():
            self.after(self.auto_save_interval_ms, self.auto_save_state)
        else:
             logger.warning("UI 視窗不存在，跳過初始自動儲存安排。")

        # 主 UI 執行緒的看門狗
        self.last_alive_check = time.time()
        self.ui_watchdog_interval_ms = 30000 # 每 30 秒檢查一次
        # 安排第一次 UI 看門狗檢查
        if self.winfo_exists():
            self.after(self.ui_watchdog_interval_ms, self.check_alive)

    def _setup_ui_config(self):
        """設定基本 UI 外觀和選單列。"""
        self.configure(bg='light gray')
        style = ttk.Style()
        style.theme_use('clam') # ttk 小部件使用 'clam' 主題

        # 設定 Tkinter 預設字體以支援中文
        try:
            # 獲取並配置預設字體
            default_font = tkFont.nametofont("TkDefaultFont")
            default_font.configure(family=CHINESE_FONT, size=9)
            # 確保其他Tkinter預設字體也使用中文字體
            tkFont.nametofont("TkTextFont").configure(family=CHINESE_FONT, size=9)
            tkFont.nametofont("TkMenuFont").configure(family=CHINESE_FONT, size=9)
            tkFont.nametofont("TkHeadingFont").configure(family=CHINESE_FONT, size=10, weight="bold")
            tkFont.nametofont("TkFixedFont").configure(family=CHINESE_FONT, size=9)

        except tkFont.FontErrors as e:
            logger.warning(f"Failed to configure default Tkinter font: {e}. Chinese characters might not display correctly.", exc_info=True)
        except Exception as e: # Catch any other potential font errors
            logger.warning(f"An unexpected error occurred while setting Tkinter default fonts: {e}", exc_info=True)


        # 創建選單列
        menu_bar = tk.Menu(self)
        self.config(menu=menu_bar) # 這行現在將正確呼叫 tk.Tk 的 config 方法，因為 self.config 不再是 configparser 物件

        # 檔案選單
        file_menu = tk.Menu(menu_bar, tearoff=0)
        file_menu.add_command(label="儲存狀態", command=self.save_state)
        file_menu.add_separator()
        file_menu.add_command(label="離開", command=self.on_exit)
        menu_bar.add_cascade(label="檔案", menu=file_menu)

        # 說明選單 (獨立)
        menu_bar.add_command(label="說明", command=self.show_help)

        # 幫助選單
        help_menu = tk.Menu(menu_bar, tearoff=0)
        help_menu.add_command(label="關於", command=self.show_about)
        menu_bar.add_cascade(label="幫助", menu=help_menu)

    def _load_state(self):
        """從配置的狀態檔案載入應用程式狀態。"""
        if os.path.exists(self.state_file):
            try:
                # 對 .npz 檔案使用 np.load，允許 pickle 處理複雜物件，如 dict/list/OrderDict
                npzfile = np.load(self.state_file, allow_pickle=True)
                saved_state = {}
                for key in npzfile.keys():
                    item = npzfile[key]
                    # 如果是0維numpy數組包裝的物件(如字典)，則取出物件本身
                    if isinstance(item, np.ndarray) and item.ndim == 0 and item.dtype == object:
                        saved_state[key] = item.item()
                    else:
                        saved_state[key] = item

                # 重建面板狀態中的 OrderedDict 和 deque
                for panel_key in ['left', 'right']:
                    if panel_key in saved_state and isinstance(saved_state[panel_key], dict):
                        panel_state_dict = saved_state[panel_key]
                        if 'daily_counts' in panel_state_dict and isinstance(panel_state_dict['daily_counts'], list):
                            panel_state_dict['daily_counts'] = OrderedDict(panel_state_dict['daily_counts'])
                        if 'last7_heatmap' in panel_state_dict and isinstance(panel_state_dict['last7_heatmap'], list):
                             # 確保 heatmap 的值是 numpy 陣列
                            panel_state_dict['last7_heatmap'] = OrderedDict(
                                (k, np.array(v) if isinstance(v, list) else v) for k, v in panel_state_dict['last7_heatmap']
                            )
                        if 'smoke_probs_history' in panel_state_dict and isinstance(panel_state_dict['smoke_probs_history'], list):
                            panel_state_dict['smoke_probs_history'] = deque(panel_state_dict['smoke_probs_history'], maxlen=60)
                        saved_state[panel_key] = panel_state_dict

                logger.info(f"已從 {self.state_file} 載入狀態")
                npzfile.close() # 關閉檔案句柄
                return saved_state
            except FileNotFoundError:
                logger.info(f"狀態檔案未找到於 {self.state_file}。")
                return {} # 如果檔案不存在則返回空狀態
            except Exception as e:
                logger.error(f"從 {self.state_file} 載入狀態錯誤: {traceback.format_exc()}", exc_info=True)
                messagebox.showerror("狀態載入錯誤", f"載入狀態檔案 {self.state_file} 錯誤:\n{e}\n以全新狀態啟動。", icon='error') # Add icon
                return {} # 如果載入失敗則返回空狀態
        else:
            logger.info(f"狀態檔案未找到於 {self.state_file}。以全新狀態啟動。")
            return {}

    def _init_panels(self):
        """初始化 VideoPanel 實例。"""
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=1)

        try:
            # 檢索每個面板的狀態，確保它是一個字典
            left_state = self.saved_state.get("left", {})
            right_state = self.saved_state.get("right", {})
        except KeyError as e:
            logger.error(f"載入面板狀態錯誤: {e}。使用預設狀態。", exc_info=True)
            left_state = {}
            right_state = {}

        # 將 config 物件和載入的狀態傳遞給面板
        self.left_panel = VideoPanel(self.main_frame, self.app_config, side="left", state=left_state) # 使用 self.app_config
        self.right_panel = VideoPanel(self.main_frame, self.app_config, side="right", state=right_state) # 使用 self.app_config

        # 為面板設定已訓練的分類器
        # 設定前檢查分類器是否已成功訓練
        if self.left_clf:
             self.left_panel.set_classifier(self.left_clf)
        else:
             logger.warning("左側分類器為 None，左側面板將不執行偵測。")
             self.left_panel.set_classifier(None) # 明確設定為 None

        if self.right_clf:
             self.right_panel.set_classifier(self.right_clf)
        else:
             logger.warning("右側分類器為 None，右側面板將不執行偵測。")
             self.right_panel.set_classifier(None) # 明確設定為 None

        # 將面板放置在網格中
        self.left_panel.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        self.right_panel.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

    def save_state(self):
        """儲存應用程式面板的當前狀態。"""
        try:
            # 從每個面板獲取狀態
            state = {
                "left": self.left_panel.get_state(),
                "right": self.right_panel.get_state()
            }

            # 使用 np.savez_compressed 儲存多個陣列/物件
            # 直接儲存 dict/list/OrderDicts/deque 需要 allow_pickle=True
            save_path = self.state_file
            np.savez_compressed(save_path, **state) # **state 將字典解包為關鍵字參數
            logger.info(f"狀態已儲存到 {save_path}")

        except Exception as e:
            logger.error(f"儲存狀態到 {self.state_file} 錯誤: {traceback.format_exc()}", exc_info=True)
            messagebox.showerror("儲存錯誤", f"無法儲存狀態: {e}", icon='error')


    def auto_save_state(self):
        """定期自動儲存狀態。"""
        # 儲存前檢查 UI 視窗是否仍然存在
        if self.winfo_exists():
            try:
                self.save_state()
            except Exception as e:
                logger.error(f"自動儲存期間發生錯誤: {e}", exc_info=True) # 記錄自動儲存錯誤，不顯示訊息框

            # 重新安排自動儲存
            if self.winfo_exists():
                 self.after(self.auto_save_interval_ms, self.auto_save_state)
            else:
                 logger.info("UI 視窗在自動儲存重新安排期間關閉。")

        else:
             logger.info("UI 視窗已關閉，停止自動儲存。")

    def check_alive(self):
        """主 UI 執行緒的看門狗。"""
        current_time = time.time()
        # 檢查 last_alive_check 是否合理地近期
        # 如果超過間隔的兩倍，則 UI 迴圈可能卡住？
        if current_time - self.last_alive_check > (self.ui_watchdog_interval_ms / 1000.0) * 2:
            logger.warning(f"UI 看門狗: 主 UI 執行緒似乎已停止。上次檢查是 {current_time - self.last_alive_check:.1f} 秒前。如果此情況頻繁發生，請考慮增加 ui_watchdog_interval_ms。")

        self.last_alive_check = current_time
        # 重新安排檢查
        if self.winfo_exists():
             self.after(self.ui_watchdog_interval_ms, self.check_alive)
        else:
             logger.info("UI 看門狗停止，視窗不存在。")

    def show_help(self):
        """顯示圖表說明對話框。"""
        help_text = """圖表說明

1. 煙霧機率曲線圖 (Smoke Probability Curve)
   • 功能：顯示最近一段時間內煙霧偵測機率的即時變化趨勢
   • 資料點：最多保存 60 個歷史點
   • 更新頻率：每次偵測處理時更新（約每秒一次）
   • Y軸範圍：0.0 到 1.0（機率值）
   • 閾值線：
     - 紅色虛線：基本煙霧閾值（預設0.5）
     - 橙色虛線：高機率閾值（預設0.95）

2. 熱力圖 (Heatmap)
   • 功能：顯示最近7天內每個半小時時段的煙霧偵測累積次數
   • 累積條件：當每秒煙霧機率 ≥ 0.5 (smoke_threshold) 時計算 +1
   • 時間範圍：最近7天
   • 時間解析度：每半小時一個區間（一天48個區間）
   • 顏色映射：黃-橙-紅漸變（YlOrRd）
   • 數值範圍：0 到 1800 (最高值30分x60秒)
   • X軸：24小時（00-23）
   • Y軸：最近7天的日期

3. 每日煙霧事件長條圖 (Daily Smoke Events Bar Chart)
   • 功能：顯示最近7天內每天的煙霧事件總數
   • 計數條件：
     - 高機率煙霧事件（≥0.95）持續≥5秒時首次計數
     - 同一事件每5分鐘重複計數一次
   • 顏色：天藍色長條
   • X軸：日期
   • Y軸：事件數量

4. 郵件通知機制 (Email Notification System)
   • 啟用條件：config.ini中設定 send_email_enabled = True
   • 觸發條件：
     - 煙霧機率 ≥ 0.95 (高機率閾值)
     - 連續持續時間 ≥ 5秒 (min_event_duration)
     - 距離上次郵件 ≥ 10分鐘 (min_email_interval)
   • 郵件內容：攝影機位置、事件時間、持續時間、嚴重度
   • 防重複機制：同一攝影機10分鐘內最多發送一次郵件
   • 發送方式：透過配置的API端點發送JSON格式郵件

更新頻率：所有圖表每3秒或超過5秒未更新時自動更新"""

        # 創建一個新的頂層視窗來顯示說明
        help_window = tk.Toplevel(self)
        help_window.title("系統說明")
        help_window.geometry("650x750")
        help_window.resizable(True, True)

        # 設定視窗圖示（如果需要）
        try:
            help_window.iconbitmap(default="")  # 使用預設圖示
        except:
            pass  # 忽略圖示設定錯誤

        # 創建文字區域和捲軸
        text_frame = tk.Frame(help_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 文字區域
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=(CHINESE_FONT, 10))
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)  # 設為唯讀

        # 捲軸
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.config(yscrollcommand=scrollbar.set)

        # 佈局
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 關閉按鈕
        close_button = tk.Button(help_window, text="關閉", command=help_window.destroy,
                                font=(CHINESE_FONT, 10))
        close_button.pack(pady=10)

        # 讓視窗居中顯示
        help_window.transient(self)  # 設定為主視窗的子視窗
        help_window.grab_set()  # 模態視窗

        # 計算居中位置
        help_window.update_idletasks()
        x = (help_window.winfo_screenwidth() // 2) - (help_window.winfo_width() // 2)
        y = (help_window.winfo_screenheight() // 2) - (help_window.winfo_height() // 2)
        help_window.geometry(f"+{x}+{y}")

    def show_about(self):
        """顯示「關於」對話框。"""
        # messagebox 預設使用系統字體，但我們可以嘗試傳遞字體
        # 注意: messagebox 的字體參數可能在所有 Tk 版本或系統上不完全一致
        messagebox.showinfo(
            "關於煙霧偵測系統",
            "煙霧偵測系統\n版本 2.0 (可配置並帶有郵件功能)\n\n© 2025 貴公司\n版權所有。",
            icon='info' # Add icon
        )

    def on_exit(self):
        """處理應用程式關閉。"""
        logger.info("應用程式正在關閉...")
        try:
            # 最後一次儲存狀態
            self.save_state()
            # 清理面板資源 (停止執行緒，釋放攝影機)
            self.left_panel.cleanup()
            self.right_panel.cleanup()
            # 清理訊號後給執行緒一點時間退出
            time.sleep(0.5)
            # 銷毀主視窗
            self.destroy()
            logger.info("應用程式關閉完成。")
        except Exception as e:
            logger.error(f"關閉期間發生錯誤: {traceback.format_exc()}", exc_info=True)
            # 即使清理失敗也要確保銷毀
            try:
                self.destroy()
            except Exception as destroy_e:
                 logger.critical(f"銷毀 UI 視窗錯誤: {destroy_e}", exc_info=True)

    def restart_application(self):
        """重新啟動整個應用程式。"""
        logger.info("正在重新啟動應用程式...")
        try:
            # 重新啟動前儲存狀態
            self.save_state()
            # 清理面板資源
            self.left_panel.cleanup()
            self.right_panel.cleanup()
            # 給執行緒一點時間退出
            time.sleep(0.5)
            # 使用 os.execl 將當前進程替換為新進程
            # sys.executable 是當前 Python 解譯器的路徑
            # sys.argv 包含腳本名稱和任何命令列參數
            logger.info(f"正在執行: {sys.executable} {' '.join(sys.argv)}")
            os.execl(sys.executable, sys.executable, *sys.argv)
        except Exception as e:
            logger.critical(f"重新啟動應用程式失敗: {traceback.format_exc()}", exc_info=True)
            # 如果可能，使用 after() 在 UI 執行緒中顯示訊息框
            if hasattr(self, 'after'):
                self.after(1, lambda: messagebox.showerror("重新啟動錯誤", f"重新啟動應用程式失敗: {e}\n正在離開。", icon='error')) # Add icon
                # 需要確保應用程式在訊息框後不會掛起
                self.after(2000, self.destroy) # 顯示訊息後銷毀視窗
            else:
                 # 如果 after 不可用 (UI 可能已死)，則僅列印並退出
                 print(f"嚴重重新啟動錯誤: {e}", file=sys.stderr)
                 sys.exit(1) # 強制退出進程

# ============================
# ConfigEditorApp 類別 (從 setup_ini3.py 複製過來)
# ============================
class ConfigEditorApp(QMainWindow):
    def __init__(self, org_config_file="config_org.ini", current_config_file="config.ini"):
        super().__init__()
        self.org_config_file = org_config_file
        self.current_config_file = current_config_file
        
        # config_template: 從 config_org.ini 載入的完整配置 (包含所有默認值)
        self.config_template = configparser.ConfigParser()
        # config_current: 從 config.ini 載入的當前配置 (僅包含已修改的值)
        self.config_current = configparser.ConfigParser()

        # 儲存 UI 元素，以便在儲存時讀取其值
        self.widgets = {} 
        # 儲存每個 (section, key) 對應的註解 (從 config_org.ini 獲取)
        self.comments = {} 

        self.setWindowTitle("Config 參數編輯器")
        self.setGeometry(100, 100, 800, 600)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # 狀態欄
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage(f"初始化中...")

        self._create_ui()
        self._load_config()

    def _create_ui(self):
        """建立應用程式的 UI 介面."""
        # 滾動區域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        self.main_layout.addWidget(scroll_area)

        # 滾動區域內的主內容容器
        scroll_content = QWidget()
        scroll_area.setWidget(scroll_content)
        self.config_layout = QVBoxLayout(scroll_content)
        self.config_layout.setAlignment(Qt.AlignTop) # 將內容頂部對齊

        # 按鈕區域
        button_layout = QVBoxLayout() 
        self.save_button = QPushButton("儲存設定")
        self.save_button.clicked.connect(self._save_config)
        button_layout.addWidget(self.save_button)

        # 添加一個說明標籤
        info_label = QLabel(f"註解來自 '{self.org_config_file}'。儲存時，只有參數值會寫入 '{self.current_config_file}'，不含註解和空行。")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: gray; font-size: 9pt;")
        button_layout.addWidget(info_label)

        self.main_layout.addLayout(button_layout)

    def _parse_template_config(self):
        """
        手動解析 config_org.ini 檔案以提取註解和實際配置。
        填充 self.config_template 和 self.comments。
        """
        self.config_template = configparser.ConfigParser()
        self.comments = {}
        
        current_section = None
        pending_comments = [] # 儲存最近讀取的註解行

        try:
            with open(self.org_config_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    stripped_line = line.strip()

                    if not stripped_line: # 空行
                        pending_comments = [] # 清除未被使用的註解
                        continue
                    
                    # 檢查是否為 section
                    if stripped_line.startswith('[') and stripped_line.endswith(']'):
                        current_section = stripped_line[1:-1]
                        if not self.config_template.has_section(current_section):
                            self.config_template.add_section(current_section)
                        pending_comments = [] # 新的 section 開始，清除之前的註解
                        continue

                    # 檢查是否為註解 (以 # 或 ; 開頭)
                    if stripped_line.startswith('#') or stripped_line.startswith(';'):
                        # 提取註解文本，移除前導的 # 或 ; 和空格
                        comment_text = stripped_line.lstrip('#; ').strip()
                        pending_comments.append(comment_text)
                        continue
                    
                    # 嘗試解析 key=value 行
                    # 考慮到可能包含 '=' 在值內部，例如 'api_url = http://...'
                    # 所以只在第一個 '=' 處分割
                    match = re.match(r'^\s*([^=\s]+)\s*=\s*(.*)$', stripped_line)
                    if match and current_section:
                        key = match.group(1).strip()
                        value = match.group(2).strip()
                        
                        self.config_template.set(current_section, key, value)
                        
                        # 如果有待處理的註解，則將其與當前 key 關聯
                        if pending_comments:
                            self.comments[(current_section, key)] = "\n".join(pending_comments)
                            pending_comments = [] # 註解已被使用，清除
                        continue
                    
                    # 如果不是 section, 註解, 或 key=value，則清除待處理註解
                    pending_comments = []

        except FileNotFoundError:
            raise FileNotFoundError(f"'{self.org_config_file}' 檔案未找到。請確保它存在且包含預設配置。")
        except Exception as e:
            raise Exception(f"解析 '{self.org_config_file}' 失敗: {e}")


    def _load_config(self):
        """從 config_org.ini 載入模板及說明，並從 config.ini 載入當前值以更新 UI."""
        try:
            # 1. 首先解析模板配置 (包含所有預設值和註解)
            self._parse_template_config()

            # 2. 載入當前配置 (如果存在的話)
            if os.path.exists(self.current_config_file):
                self.config_current.read(self.current_config_file, encoding='utf-8')
                self.statusBar.showMessage(f"已從 '{self.org_config_file}' 載入模板，並從 '{self.current_config_file}' 載入當前值。")
            else:
                self.statusBar.showMessage(f"已從 '{self.org_config_file}' 載入模板。'{self.current_config_file}' 不存在，將使用默認值。")
            
            # 清空舊的 UI 元素
            self._clear_layout(self.config_layout)
            self.widgets.clear()

            # 3. 根據模板配置動態生成 UI，值優先從 config_current 獲取
            for section in self.config_template.sections():
                group_box = QGroupBox(section)
                form_layout = QFormLayout()
                group_box.setLayout(form_layout)
                self.config_layout.addWidget(group_box)

                for key, default_value in self.config_template.items(section):
                    # 獲取實際顯示的值：優先從 config_current 獲取，否則使用 template 的默認值
                    if self.config_current.has_option(section, key):
                        current_value = self.config_current.get(section, key)
                    else:
                        current_value = default_value

                    # 1. 處理鍵的標籤 (可以包含註解)
                    key_display_label = QLabel(key)
                    key_display_label.setFont(QFont("Arial", 10, QFont.Bold)) # 加粗鍵名

                    # 創建一個容器 Widget 來包含鍵名和可能的註解
                    label_container = QWidget()
                    label_vbox = QVBoxLayout(label_container)
                    label_vbox.setContentsMargins(0, 0, 0, 0) # 移除佈局的默認邊距

                    # 檢查是否有註解
                    comment_text = self.comments.get((section, key), "")
                    
                    if comment_text:
                        comment_label = QLabel(comment_text)
                        comment_label.setStyleSheet("color: gray;")
                        comment_label.setWordWrap(True) # 自動換行
                        comment_label.setFont(QFont("Arial", 9)) # 註解字體稍小

                        label_vbox.addWidget(key_display_label)
                        label_vbox.addWidget(comment_label)
                        label_vbox.addStretch(1) # 讓註解與下方內容之間保持間距
                    else:
                        # 如果沒有註解，僅將鍵名標籤添加到容器的垂直佈局中
                        label_vbox.addWidget(key_display_label)
                        label_vbox.addStretch(1)

                    # 2. 處理值的輸入部件
                    # 嘗試判斷是否為布林值 (不區分大小寫)
                    if current_value.lower() in ['true', 'false']:
                        widget = QCheckBox()
                        # configparser.getboolean() 可以處理 'True', 'False', 'yes', 'no', 'on', 'off', '1', '0'
                        # 這裡的邏輯需要微調：應該是基於 current_value 來判斷，而不是 template
                        if current_value.lower() == 'true':
                            widget.setChecked(True)
                        else:
                            widget.setChecked(False)
                    elif key == 'body_template':
                        # 對於 body_template 使用多行文字框
                        widget = QTextEdit(current_value)
                        widget.setMaximumHeight(40)  # 設定最大高度約3行
                        widget.setMinimumHeight(30)  # 設定最小高度約2行
                    else:
                        widget = QLineEdit(current_value)
                    
                    # 將容器 Widget (label_container) 作為標籤部分傳入 addRow
                    form_layout.addRow(label_container, widget)
                    self.widgets[(section, key)] = widget # 儲存 widget 的參考

        except FileNotFoundError as e:
            QMessageBox.critical(self, "檔案未找到", str(e))
            self.statusBar.showMessage(f"載入失敗: {e}")
        except Exception as e:
            QMessageBox.critical(self, "載入錯誤", f"載入配置失敗: {e}")
            self.statusBar.showMessage(f"載入失敗。")

    def _save_config(self):
        """將 UI 中的參數值儲存回 config.ini 檔案 (不包含註解)."""
        new_config_to_save = configparser.ConfigParser()
        
        try:
            # 從 UI 元素中讀取最新的值，並填充到新的 config 對象中
            for (section, key), widget in self.widgets.items():
                if not new_config_to_save.has_section(section):
                    new_config_to_save.add_section(section)

                if isinstance(widget, QCheckBox):
                    new_config_to_save[section][key] = str(widget.isChecked())
                elif isinstance(widget, QTextEdit):
                    new_config_to_save[section][key] = widget.toPlainText()
                elif isinstance(widget, QLineEdit):
                    new_config_to_save[section][key] = widget.text()

            # 寫入檔案
            with open(self.current_config_file, 'w', encoding='utf-8') as configfile:
                new_config_to_save.write(configfile)
            
            # 儲存後，重新載入配置以確保 UI 反映最新儲存狀態 (可選，但有助於同步)
            # 不在此處調用 _load_config，讓 main 函數負責重新載入
            self.statusBar.showMessage(f"成功儲存設定至 '{self.current_config_file}'。")
            QMessageBox.information(self, "儲存成功", f"設定已成功儲存至 '{self.current_config_file}'。")

        except Exception as e:
            QMessageBox.critical(self, "儲存錯誤", f"儲存配置失敗: {e}")
            self.statusBar.showMessage(f"儲存失敗。")

    def _clear_layout(self, layout):
        """清除指定佈局中的所有 widget 和子佈局."""
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
                if item.layout():
                    self._clear_layout(item.layout())


# ============================
# 8. 主程式入口
# ============================
def main():
    logger.info("main() 開始")
    # --- 在載入組態之前的初始日誌設定 ---
    # 這是運行腳本時的真正入口點。
    # 在呼叫任何可能記錄的其他函數之前，在此處設定基本日誌。
    # load_config 函數隨後將根據組態檔案重新配置日誌。
    # (初始日誌設定已移至 __main__ 區塊的開頭)

    # 載入組態
    config = load_config()
    if config is None:
        logger.critical("載入組態失敗。正在離開。")
        # 尚無活動 UI，可以直接退出
        sys.exit(1)

    # 獲取組態值
    logger.info("開始獲取組態值")
    left_dataset_dir = config.get('LeftCamera', 'dataset_dir')
    right_dataset_dir = config.get('RightCamera', 'dataset_dir')
    left_cache_dir = config.get('LeftCamera', 'cache_dir')
    right_cache_dir = config.get('RightCamera', 'cache_dir')
    left_config_path = config.get('LeftCamera', 'roi_config_path')
    right_config_path = config.get('RightCamera', 'roi_config_path')

    left_image_save_dir = config.get('LeftCamera', 'image_save_dir')
    right_image_save_dir = config.get('RightCamera', 'image_save_dir')

    try:
        fixed_size = (config.getint('Detection', 'fixed_image_width'),
                      config.getint('Detection', 'fixed_image_height'))
    except ValueError:
        logger.error("fixed_image_width 或 fixed_image_height 不是有效的整數。使用預設值 (128, 128)。", exc_info=True)
        fixed_size = (128, 128)

    feature_config = config['Features']
    classifier_config = config['Classifier']
    # email_config = config['EmailSettings'] # email_config 已在 VideoPanel 中獲取

    # 創建必要的目錄
    logger.info("獲取組態值完成")
    try:
        os.makedirs(left_image_save_dir, exist_ok=True)
        os.makedirs(right_image_save_dir, exist_ok=True)
        os.makedirs(left_cache_dir, exist_ok=True) # 確保快取目錄存在
        os.makedirs(right_cache_dir, exist_ok=True)
        logger.info(f"目錄已確保: {left_image_save_dir}, {right_image_save_dir}, {left_cache_dir}, {right_cache_dir}")
    except Exception as e:
        logger.critical(f"創建必要目錄失敗: {e}", exc_info=True)
        sys.exit(1)

    logger.info("創建必要目錄完成")
    logger.info("啟動程式 V2.0 (可配置並帶有郵件功能)...")

    # 載入 ROI
    left_rois = load_roi_from_config(left_config_path)
    right_rois = load_roi_from_config(right_config_path)
    if not left_rois:
        logger.warning(f"在 {left_config_path} 中未找到左側攝影機的 ROI 定義。左側攝影機的訓練/偵測可能無法進行。")
    if not right_rois:
        logger.warning(f"在 {right_config_path} 中未找到右側攝影機的 ROI 定義。右側攝影機的訓練/偵測可能無法進行。")

    logger.info("載入 ROI 完成")
    # --- 選單的主程式迴圈 ---
    # 保留訓練/偵測的獨立選單選項
    left_clf = None
    right_clf = None

    while True:
        print("\n=== 煙霧偵測系統 ===")
        print("1. 訓練左側分類器")
        print("2. 訓練右側分類器")
        print("3. 參數設定") # 新增選項
        print("4. 執行偵測系統")
        print("5. 離開") # 選項編號變更
        selection = input("請選擇: ").strip()

        if selection == "5": # 選項編號變更
            logger.info("使用者從選單退出程式。")
            break # 退出主選單迴圈

        elif selection == "1":
            logger.info("正在訓練左側分類器...")
            try:
                # 載入或萃取左側攝影機的特徵
                left_X, left_y = load_cached_features(left_cache_dir)
                if len(left_X) == 0:
                    logger.info("未找到或左側快取為空，正在從資料集萃取特徵...")
                    # 將必要的組態傳遞給 load_dataset
                    left_X, left_y = load_dataset(left_dataset_dir, rois=left_rois, feature_config=feature_config, fixed_size=fixed_size)
                    if len(left_X) > 0:
                         cache_features(left_cache_dir, left_X, left_y)
                         logger.info(f"已從左側資料集萃取 {len(left_X)} 個樣本。")
                    else:
                         logger.error(f"未從位於 {left_dataset_dir} 的左側資料集萃取到樣本。無法訓練。")
                         print("未找到用於左側訓練的樣本。請檢查資料集。")
                         continue # 返回選單

                logger.info(f"正在使用 {len(left_X)} 個樣本進行左側訓練。")
                # 將 classifier_config 傳遞給 train_classifier
                left_clf = train_classifier(left_X, left_y, classifier_config=classifier_config)
                if left_clf:
                    logger.info("左側分類器訓練成功完成。")
                    print("左側分類器已訓練。")
                else:
                    logger.error("左側分類器訓練失敗。")
                    print("左側分類器訓練失敗。")

            except Exception as e:
                logger.error(f"左側分類器訓練過程中發生錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"訓練期間發生錯誤: {e}")

        elif selection == "2":
            logger.info("正在訓練右側分類器...")
            try:
                # 載入或萃取右側攝影機的特徵
                right_X, right_y = load_cached_features(right_cache_dir)
                if len(right_X) == 0:
                    logger.info("未找到或右側快取為空，正在從資料集萃取特徵...")
                    # 將必要的組態傳遞給 load_dataset
                    right_X, right_y = load_dataset(right_dataset_dir, rois=right_rois, feature_config=feature_config, fixed_size=fixed_size)
                    if len(right_X) > 0:
                        cache_features(right_cache_dir, right_X, right_y)
                        logger.info(f"已從右側資料集萃取 {len(right_X)} 個樣本。")
                    else:
                        logger.error(f"未從位於 {right_dataset_dir} 的右側資料集萃取到樣本。無法訓練。")
                        print("未找到用於右側訓練的樣本。請檢查資料集。")
                        continue # 返回選單

                logger.info(f"正在使用 {len(right_X)} 個樣本進行右側訓練。")
                # 將 classifier_config 傳遞給 train_classifier
                right_clf = train_classifier(right_X, right_y, classifier_config=classifier_config)
                if right_clf:
                    logger.info("右側分類器訓練成功完成。")
                    print("右側分類器已訓練。")
                else:
                    logger.error("右側分類器訓練失敗。")
                    print("右側分類器訓練失敗。")

            except Exception as e:
                logger.error(f"右側分類器訓練過程中發生錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"訓練期間發生錯誤: {e}")

        elif selection == "3": # 新增參數設定選項
            logger.info("正在啟動參數設定介面...")
            try:
                # 創建 QApplication 實例，如果它還不存在的話
                # 確保在任何 QWidget 實例化之前創建 QApplication
                app_pyqt = QApplication.instance()
                if app_pyqt is None:
                    app_pyqt = QApplication(sys.argv)
                    app_pyqt.setQuitOnLastWindowClosed(True) # 當最後一個視窗關閉時自動退出應用程式

                editor = ConfigEditorApp(org_config_file="config_org.ini", current_config_file="config.ini")
                editor.show()
                app_pyqt.exec_() # 這會阻塞並運行 PyQt 事件循環，直到編輯器窗口關閉
                
                logger.info("參數設定介面已關閉。")
                
                # 重新載入 config.ini，以便主程式的後續操作能讀取到最新的設定
                # 這裡的 config 變量是 main() 的局部變量，需要更新它
                new_config = load_config()
                if new_config:
                    config = new_config # 更新 main 函數中的 config 變量
                    logger.info("Config 檔案已在參數編輯器關閉後重新載入。")
                else:
                    logger.error("參數編輯器關閉後重新載入 Config 失敗。將繼續使用之前的設定。")

            except Exception as e:
                logger.error(f"啟動參數設定介面錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"啟動參數設定介面錯誤: {e}")

        elif selection == "4": # 選項編號變更
            logger.info("正在啟動偵測模式...")
            # 在偵測模式下，我們應始終使用最新的已訓練模型。
            # 因此，嘗試從快取載入並在快取存在時進行訓練。
            # 如果快取不存在，則提示使用者先進行訓練。
            try:
                # 嘗試載入並訓練左側分類器
                current_left_clf = None
                left_X_ui, left_y_ui = load_cached_features(left_cache_dir) # 使用不同的變數名以避免覆蓋選單中訓練的分類器
                if len(left_X_ui) == 0:
                    logger.error(f"在快取 ({left_cache_dir}) 中未找到左側特徵。請先訓練左側分類器。")
                    print("未找到左側特徵。請先訓練左側分類器。")
                else:
                     logger.info("成功載入快取的左側特徵。正在為 UI 訓練左側分類器...")
                     current_left_clf = train_classifier(left_X_ui, left_y_ui, classifier_config=classifier_config)
                     if current_left_clf is None:
                         logger.error("從快取訓練左側分類器失敗。")
                         print("從快取特徵訓練左側分類器失敗。")

                # 嘗試載入並訓練右側分類器
                current_right_clf = None
                right_X_ui, right_y_ui = load_cached_features(right_cache_dir)
                if len(right_X_ui) == 0:
                    logger.error(f"在快取 ({right_cache_dir}) 中未找到右側特徵。請先訓練右側分類器。")
                    print("未找到右側特徵。請先訓練右側分類器。")
                else:
                     logger.info("成功載入快取的右側特徵。正在為 UI 訓練右側分類器...")
                     current_right_clf = train_classifier(right_X_ui, right_y_ui, classifier_config=classifier_config)
                     if current_right_clf is None:
                         logger.error("從快取訓練右側分類器失敗。")
                         print("從快取特徵訓練右側分類器失敗。")

                # 檢查是否至少有一個分類器成功訓練
                if current_left_clf is None and current_right_clf is None:
                     logger.critical("兩個分類器都載入或訓練失敗。無法啟動偵測。")
                     print("兩個分類器都失敗。無法啟動偵測。")
                     continue # 返回選單

                # 啟動 Tkinter UI
                logger.info("正在啟動 UI...")
                # 將整個 config 物件和可能為 None 的分類器傳遞給 UI
                app = SmokeDetectionUI(config, current_left_clf, current_right_clf)
                app.mainloop()
                logger.info("UI 已關閉。")
                # mainloop 退出後，程式將從此處繼續。
                # 如果我們希望在關閉 UI 後退出主程式，則應中斷。
                break # UI 完成後退出主選單迴圈

            except Exception as e:
                logger.error(f"偵測模式錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"偵測模式錯誤: {e}")

        else:
            print("無效選擇，請重試")

    logger.info("程式正常結束。")
    logger.info("main() 結束")

if __name__ == "__main__":
    print("__main__ block started")
    print("Setting up initial logger formatter")
    # 這是運行腳本時的真正入口點。
    # 在呼叫任何可能記錄的其他函數之前，在此處設定基本日誌。
    # load_config 函數隨後將根據組態檔案重新配置日誌。
    # (初始日誌設定已移至 __main__ 區塊的開頭)

    # 將初始處理器添加到全域日誌實例。
    # 這確保我們正在配置 *該* 特定的日誌物件。

    # 將初始處理器添加到全域日誌實例。
    # 這確保我們正在配置 *該* 特定的日誌物件。
    initial_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    print("Initial logger formatter set")

    # 嘗試添加 FileHandler (如果檔案權限有問題可能會失敗)
    print("Attempting to add FileHandler")
    try:
        print("Inside FileHandler try block")
        initial_file_handler = logging.FileHandler("smoke_detection.log", mode='a', encoding='utf-8')
        initial_file_handler.setFormatter(initial_formatter)
        # 僅在尚未添加時添加 (在某些複雜的導入情況下可能會發生)
        if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
            print("Adding FileHandler")
            logger.addHandler(initial_file_handler)
    except Exception as e:
        logger.error(f"無法設定初始檔案日誌: {e}", exc_info=True)
    print("FileHandler setup complete")

    # 始終為控制台輸出添加 StreamHandler
    print("Attempting to add StreamHandler")
    initial_stream_handler = logging.StreamHandler(sys.stdout)
    initial_stream_handler.setFormatter(initial_formatter)
    if not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        print("Adding StreamHandler")
        logger.addHandler(initial_stream_handler)
    print("StreamHandler setup complete")

    # 設定預設日誌等級，直到載入組態
    print("Setting default log level")
    logger.setLevel(logging.INFO)
    print("Default log level set")

    # 現在呼叫 main()。load_config 將首先在 main 內部運行並重新配置日誌。
    print("Calling main()")
    try:
        main()
    except KeyboardInterrupt:
        # 此區塊捕獲終端機中的 Ctrl+C
        logger.info("程式被使用者中斷 (KeyboardInterrupt)")
    except Exception as e:
        # 此區塊捕獲任何未處理的異常
        logger.critical(f"主執行中未處理的異常: {traceback.format_exc()}", exc_info=True)
        print(f"嚴重錯誤: {e}", file=sys.stderr) # 如果日誌損壞，也列印到控制台
