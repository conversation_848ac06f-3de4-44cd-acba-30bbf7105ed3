# -*- coding: utf-8 -*-
print("<PERSON>rip<PERSON> started")
import cv2
import os
import sys
import numpy as np
import json
import time
import datetime
import logging
import traceback
import threading
import warnings
import gc
from collections import deque, OrderedDict
from skimage.feature import hog, local_binary_pattern
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw, ImageFont
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import csv
import configparser
import requests
import tkinter.font as tkFont
import matplotlib.colorbar

import re
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QFormLayout, QLineEdit, QCheckBox, QPushButton,
    Q<PERSON><PERSON><PERSON><PERSON><PERSON>, QGroupBox, Q<PERSON>abel, <PERSON><PERSON><PERSON><PERSON><PERSON>ar,
    Q<PERSON>essageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

global logger
logger = logging.getLogger("SmokeDetection")

CHINESE_FONT = 'Microsoft YaHei'

try:
    FONT_PATH_FOR_OPENCV = "C:/Windows/Fonts/msyh.ttc"
    if not os.path.exists(FONT_PATH_FOR_OPENCV):
        FONT_PATH_FOR_OPENCV = "C:/Windows/Fonts/simhei.ttf"
    if not os.path.exists(FONT_PATH_FOR_OPENCV):
        logger.warning(f"未能找到指定的中文字體文件用於OpenCV繪製: {FONT_PATH_FOR_OPENCV}。OpenCV文字可能無法顯示中文。")
        FONT_PATH_FOR_OPENCV = None
except Exception as e:
    logger.error(f"獲取中文字體文件路徑時發生錯誤: {e}", exc_info=True)
    FONT_PATH_FOR_OPENCV = None


plt.rcParams['font.sans-serif'] = [CHINESE_FONT] + plt.rcParams['font.sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def load_config(config_file='config.ini'):
    try:
        logger.info("load_config() 開始")
    except Exception as e:
        print(f"Error during initial logger.info in load_config: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        sys.exit(1)

    config = configparser.ConfigParser()

    if not os.path.exists(config_file):
        logger.error(f"組態檔案未找到: {config_file}")
        logger.info(f"已建立預設組態檔案: {config_file}。請編輯它。")
        return None

    try:
        config.read(config_file, encoding='utf-8')
        logger.info(f"組態已從 {config_file} 載入")

        log_level_str = config.get('General', 'log_level', fallback='INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)

        for handler in logger.handlers[:]:
             logger.removeHandler(handler)

        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler = logging.FileHandler("smoke_detection.log", mode='a', encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

        logger.setLevel(log_level)
        logger.info(f"日誌等級已設定為 {log_level_str}")

        if config.has_section('FFMPEG') and config.has_option('FFMPEG', 'capture_options') and 'capture_options' in config['FFMPEG']:
             options = config.get('FFMPEG', 'capture_options')
             os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = options
             logger.info(f"FFMPEG 擷取選項已設定: {options}")
        else:
             os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "rtsp_transport;tcp|analyzeduration;0|fflags;nobuffer|stimeout;1000000"
             logger.info("正在使用預設 FFMPEG 擷取選項。")

        warnings.filterwarnings("ignore", category=RuntimeWarning)
        warnings.filterwarnings("ignore", category=UserWarning)
        warnings.filterwarnings("ignore", category=FutureWarning)

        logger.info("load_config() 結束")
        return config
    
    except configparser.Error as e:
        logger.error(f"解析組態檔案 {config_file} 錯誤: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"載入組態檔案 {config_file} 錯誤: {traceback.format_exc()}", exc_info=True)
        return None

def format_date(d):
    # This function now correctly formats a date object into a 'M/D' string key
    # which matches the keys generated by the improved _read_sun_times_from_csv
    s = d.strftime("%m/%d")
    if s.startswith("0"):
        s = s[1:]
    parts = s.split("/")
    if len(parts) == 2:
       if parts[1].startswith("0"):
           parts[1] = parts[1][1:]
       s = "/".join(parts)
    return s

def extract_features(image, roi_vertices=None, feature_config=None, fixed_size=(128,128)):
    try:
        if image is None:
            return None

        if feature_config is None:
            logger.error("未提供特徵組態。")
            return None

        use_hog = feature_config.getboolean('use_hog', False)
        hog_params_str = feature_config.get('hog_params', '16,16,2,2').split(',')
        try:
            hog_ppc = (int(hog_params_str[0]), int(hog_params_str[1]))
            hog_cpb = (int(hog_params_str[2]), int(hog_params_str[3]))
        except (ValueError, IndexError):
             logger.error(f"組態中 hog_params 格式無效: {hog_params_str}。使用預設值 16,16,2,2", exc_info=True)
             hog_ppc = (16, 16)
             hog_cpb = (2, 2)

        use_lbp = feature_config.getboolean('use_lbp', False)
        lbp_params_str = feature_config.get('lbp_params', '8,1,uniform').split(',')
        try:
            lbp_p = int(lbp_params_str[0])
            lbp_r = int(lbp_params_str[1])
            lbp_method = lbp_params_str[2] if len(lbp_params_str) > 2 else 'uniform'
        except (ValueError, IndexError):
             logger.error(f"組態中 lbp_params 格式無效: {lbp_params_str}。使用預設值 8,1,uniform", exc_info=True)
             lbp_p = 8
             lbp_r = 1
             lbp_method = 'uniform'

        use_sift = feature_config.getboolean('use_sift', False)

        if roi_vertices is not None:
            roi_vertices_array = np.array(roi_vertices, dtype=np.int32)
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            if len(roi_vertices_array) >= 3:
                cv2.drawContours(mask, [roi_vertices_array.reshape((-1, 1, 2))], -1, 255, thickness=cv2.FILLED)
            else:
                logger.warning(f"提供的 ROI 頂點數量無效 ({len(roi_vertices_array)})，已跳過。")
                return None

            roi_image = cv2.bitwise_and(image, image, mask=mask)
            if np.any(mask):
                 x, y, w, h = cv2.boundingRect(mask)
                 x = max(0, x)
                 y = max(0, y)
                 w = min(w, image.shape[1] - x)
                 h = min(h, image.shape[0] - y)

                 if w > 0 and h > 0:
                      roi_image_cropped = roi_image[y:y+h, x:x+w]
                 else:
                      logger.warning("邊界矩形後的 ROI 尺寸無效 (寬或高 <=0)。無法裁切。")
                      return None
            else:
                 logger.warning("ROI 遮罩為全黑。沒有有效區域。")
                 return None

            if roi_image_cropped.shape[0] > 0 and roi_image_cropped.shape[1] > 0:
                 image_processed = cv2.resize(roi_image_cropped, fixed_size)
            else:
                 logger.warning("裁切後的 ROI 影像尺寸為零。無法調整尺寸。")
                 return None
        else:
            image_processed = cv2.resize(image, fixed_size)

        if len(image_processed.shape) == 3 and image_processed.shape[2] == 3:
            gray = cv2.cvtColor(image_processed, cv2.COLOR_BGR2GRAY)
        else:
            gray = image_processed

        features = []

        if use_hog:
            try:
                if gray.shape[0] > 0 and gray.shape[1] > 0:
                     hog_features = hog(gray, pixels_per_cell=hog_ppc, cells_per_block=hog_cpb, visualize=False)
                     features.append(hog_features)
                else:
                     logger.warning("灰度影像為空，無法進行 HOG 特徵萃取。")

            except Exception as hog_e:
                 logger.error(f"HOG 特徵萃取錯誤: {hog_e}", exc_info=True)

        if use_lbp:
            try:
                if gray.shape[0] > 0 and gray.shape[1] > 0:
                    lbp = local_binary_pattern(gray, P=lbp_p, R=lbp_r, method=lbp_method)
                    if lbp_method == 'uniform':
                        n_bins = lbp_p + 2
                    elif lbp_method == 'nri_uniform':
                         n_bins = lbp_p*(lbp_p-1) + 3
                    else:
                        n_bins = 2**lbp_p
                    (hist, _) = np.histogram(lbp.ravel(), bins=n_bins, range=(0, n_bins))
                    hist = hist.astype("float")
                    hist /= (hist.sum() + 1e-6)
                    features.append(hist)
                else:
                     logger.warning("灰度影像為空，無法進行 LBP 特徵萃取。")
            except Exception as lbp_e:
                logger.error(f"LBP 特徵萃取錯誤: {lbp_e}", exc_info=True)

        if use_sift:
            try:
                if not hasattr(cv2, 'SIFT_create'):
                     logger.warning("請求使用 SIFT，但未安裝 (需要 opencv-contrib-python)。已跳過 SIFT。")
                elif gray.shape[0] > 0 and gray.shape[1] > 0:
                    sift = cv2.SIFT_create()
                    keypoints, descriptors = sift.detectAndCompute(gray, None)
                    if descriptors is not None and descriptors.shape[0] > 0:
                        sift_features = np.mean(descriptors, axis=0)
                        features.append(sift_features)
                    else:
                        logger.debug("影像中未找到 SIFT 關鍵點。")
                        features.append(np.zeros(128))
                else:
                     logger.warning("灰度影像為空，無法進行 SIFT 特徵萃取。")

            except Exception as sift_e:
                logger.error(f"SIFT 特徵萃取錯誤: {traceback.format_exc()}", exc_info=True)

        if not features:
             return None

        return np.concatenate(features)
    except Exception as e:
        logger.error(f"特徵萃取錯誤: {traceback.format_exc()}", exc_info=True)
        return None

def load_dataset(dataset_dir, rois, feature_config, fixed_size):
    data = []
    labels = []
    logger.info(f"正在從 {dataset_dir} 載入資料集")
    if not os.path.exists(dataset_dir):
        logger.error(f"資料集目錄未找到: {dataset_dir}")
        return np.array(data), np.array(labels)

    for label_dir in ['smoke', 'no_smoke']:
        dir_path = os.path.join(dataset_dir, label_dir)
        if not os.path.exists(dir_path):
            logger.warning(f"目錄 {dir_path} 不存在")
            continue
        file_count = 0
        target_label = 1 if label_dir=="smoke" else 0
        image_files = [f for f in os.listdir(dir_path) if f.lower().endswith(('.jpg','.jpeg','.png','.bmp'))]
        logger.info(f"在 {label_dir} 目錄中找到 {len(image_files)} 張影像。")
        
        for filename in image_files:
            file_path = os.path.join(dir_path, filename)
            image = cv2.imread(file_path)
            if image is None:
                logger.warning(f"無法載入影像 {file_path}")
                continue
            try:
                features = None
                if rois is not None and len(rois) > 0:
                     all_roi_features = []
                     for i, roi in enumerate(rois):
                         if not (isinstance(roi, list) and len(roi) >= 3 and all(isinstance(p, list) and len(p) == 2 for p in roi)):
                             logger.warning(f"影像 {filename} 的 ROI {i} 格式無效。已跳過此 ROI。")
                             continue

                         feats = extract_features(image, roi_vertices=roi, feature_config=feature_config, fixed_size=fixed_size)
                         if feats is not None:
                             all_roi_features.append(feats)

                     if all_roi_features:
                         feature_shapes = [f.shape for f in all_roi_features]
                         if len(set(feature_shapes)) == 1:
                             features = np.mean(all_roi_features, axis=0)
                         else:
                             logger.error(f"影像 {filename} 的 ROI 特徵形狀不一致: {feature_shapes}。無法聚合。")
                             continue
                     else:
                         logger.warning(f"在 {file_path} 中沒有從任何 ROI 萃取到特徵。已跳過影像。")
                         continue
                else:
                    logger.info(f"未定義 ROI，正在從整個影像 {filename} 中萃取特徵。")
                    features = extract_features(image, roi_vertices=None, feature_config=feature_config, fixed_size=fixed_size)
                    if features is None:
                        logger.warning(f"沒有從整個影像 {file_path} 萃取到特徵。已跳過。")
                        continue

                if isinstance(features, np.ndarray) and features.size > 0:
                     data.append(features)
                     labels.append(target_label)
                     file_count += 1
                else:
                     logger.warning(f"特徵萃取導致 {file_path} 的資料為空或無效。已跳過。")

            except Exception as e:
                logger.error(f"處理 {file_path} 錯誤: {traceback.format_exc()}", exc_info=True)
        logger.info(f"成功處理 {label_dir} 目錄中的 {file_count} 張影像")

    logger.info(f"完成從 {dataset_dir} 載入資料集。總樣本數: {len(data)}")
    return np.array(data), np.array(labels)

def cache_features(cache_dir, X, y):
    try:
        if not os.path.exists(cache_dir):
             os.makedirs(cache_dir, exist_ok=True)

        if X is None or y is None or len(X) == 0 or len(X) != len(y):
             logger.warning("沒有資料可快取或資料無效。")
             return

        cache_path = os.path.join(cache_dir, "features.npz")
        np.savez_compressed(cache_path, X=X, y=y)
        logger.info(f"特徵已快取到 {cache_path}")
    except Exception as e:
        logger.error(f"快取特徵到 {cache_dir} 錯誤: {traceback.format_exc()}", exc_info=True)

def load_cached_features(cache_dir):
    try:
        cache_path = os.path.join(cache_dir, "features.npz")
        if not os.path.exists(cache_path):
            logger.info(f"快取檔案未找到於 {cache_path}")
            return np.array([]), np.array([])
        npzfile = np.load(cache_path, allow_pickle=True)
        X = npzfile['X']
        y = npzfile['y']
        npzfile.close()

        if len(X) == 0:
             logger.warning(f"快取特徵檔案 {cache_path} 為空。")

        logger.info(f"已從 {cache_dir} 載入 {len(X)} 個快取特徵")
        return X, y
    except FileNotFoundError:
         logger.info(f"快取檔案未找到: {cache_dir}/features.npz")
         return np.array([]), np.array([])
    except Exception as e:
        logger.error(f"從 {cache_dir} 載入快取特徵錯誤: {traceback.format_exc()}", exc_info=True)
        return np.array([]), np.array([])

def train_classifier(X, y, classifier_config):
    try:
        if len(X) == 0 or len(y) == 0:
            logger.warning("沒有資料可訓練分類器。")
            return None

        num_samples = len(X)
        unique_classes = np.unique(y)
        num_classes = len(unique_classes)

        if num_samples < 2:
             logger.error(f"訓練資料僅有 {num_samples} 個樣本。無法訓練。")
             return None

        svm_probability = classifier_config.getboolean('svm_probability', True)
        if svm_probability and num_classes < 2:
            logger.error(f"僅找到 {num_classes} 個唯一類別 ({unique_classes})。無法在 probability=True 的情況下訓練，這至少需要兩個類別。")
            return None

        if num_samples >= 5:
            try:
                 X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y if num_classes > 1 else None)
            except ValueError as e:
                 logger.warning(f"無法執行分層分割: {e}。退回簡單分割。")
                 X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            logger.info(f"分割資料: 訓練集={len(X_train)} 個樣本, 測試集={len(X_test)} 個樣本。")
        else:
            logger.warning(f"只有 {num_samples} 個樣本。在整個資料集上進行訓練，不執行測試分割。")
            X_train, y_train = X, y
            X_test, y_test = np.array([]), np.array([])

        svm_kernel = classifier_config.get('svm_kernel', 'linear')

        logger.info(f"正在訓練 SVC，核函數='{svm_kernel}'，機率={svm_probability}")

        clf = SVC(kernel=svm_kernel, probability=svm_probability, random_state=42)
        clf.fit(X_train, y_train)

        if len(X_test) > 0:
            y_pred = clf.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"測試準確度: {accuracy:.4f}")
            try:
                target_names = [str(c) for c in clf.classes_]
                report = classification_report(y_test, y_pred, target_names=target_names, zero_division=0)
                logger.info("\n分類報告:\n" + report)
            except ValueError as e:
                 logger.warning(f"無法生成分類報告: {e}。可能是由於測試集中缺少類別或類別不符合預期。")
            except Exception as e:
                 logger.error(f"生成分類報告錯誤: {e}", exc_info=True)
        else:
            logger.warning("分割後沒有可用於評估的測試資料。")

        return clf
    except Exception as e:
        logger.error(f"訓練分類器錯誤: {traceback.format_exc()}", exc_info=True)
        return None

def predict_image_multiple_rois(image, rois, clf, feature_config, detection_config, classifier_config):
    if image is None or clf is None or not rois:
        if not rois:
            logger.debug("predict_image_multiple_rois called with no ROIs defined. Returning no smoke.")
        return "無煙霧", []

    fixed_size = (detection_config.getint('fixed_image_width', 128),
                  detection_config.getint('fixed_image_height', 128))
    smoke_threshold = detection_config.getfloat('smoke_threshold', 0.5)
    svm_probability_enabled = classifier_config.getboolean('svm_probability', True)

    results = []
    try:
        if not hasattr(clf, 'predict'):
             logger.error("分類器物件缺少 'predict' 方法。")
             return "錯誤", [(0, 0.0)] * len(rois)

        if svm_probability_enabled and not hasattr(clf, 'predict_proba'):
             logger.warning("組態中啟用了分類器機率計算，但缺少 'predict_proba' 方法。將預測結果作為機率 (1.0 或 0.0)。")
             svm_probability_enabled = False

        for i, roi in enumerate(rois):
            if not (isinstance(roi, list) and len(roi) >= 3 and all(isinstance(p, list) and len(p)==2 for p in roi)):
                logger.warning(f"跳過 ROI 索引 {i} 的無效 ROI 格式: {roi}")
                results.append((0, 0.0))
                continue

            feats = extract_features(image, roi_vertices=roi, feature_config=feature_config, fixed_size=fixed_size)
            if feats is None:
                results.append((0, 0.0))
                continue

            feats = feats.reshape(1, -1)

            try:
                pred = clf.predict(feats)[0]

                if svm_probability_enabled:
                     prob = clf.predict_proba(feats)[0]
                     if hasattr(clf, 'classes_') and 1 in clf.classes_:
                         smoke_class_index = np.where(clf.classes_ == 1)[0][0]
                         smoke_prob = prob[smoke_class_index]
                     else:
                          logger.warning("在預測期間分類器類別中未找到煙霧類別 (標籤 1)。機率設定為 0。")
                          smoke_prob = 0.0
                else:
                     smoke_prob = 1.0 if pred == 1 else 0.0

                final_roi_pred = 1 if smoke_prob >= smoke_threshold else 0

                results.append((final_roi_pred, smoke_prob))

            except Exception as predict_e:
                logger.error(f"ROI 索引 {i} 的預測錯誤: {predict_e}。跳過此 ROI 預測。", exc_info=True)
                results.append((0, 0.0))

        overall_label = "煙霧" if any(r[0] == 1 for r in results) else "無煙霧"
        return overall_label, results

    except Exception as e:
        logger.error(f"影像預測錯誤: {traceback.format_exc()}", exc_info=True)
        return "預測錯誤", [(0, 0.0)] * len(rois)

def load_roi_from_config(config_path):
    if not os.path.exists(config_path):
        logger.error(f"ROI 組態檔案未找到: {config_path}")
        return []
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        rois = []
        for i, roi_dict in enumerate(data.get("rois", [])):
            vertices = roi_dict.get("vertices")
            if (vertices and isinstance(vertices, list) and len(vertices) >= 3 and
                all(isinstance(pt, list) and len(pt)==2 for pt in vertices)):
                rois.append(vertices)
            else:
                logger.warning(f"索引 {i} 處的 ROI 定義無效 (必須是 >=3 個 [x, y] 點的列表)，已跳過: {roi_dict}")
        logger.info(f"已從 {config_path} 載入 {len(rois)} 個有效 ROI")
        return rois
    except json.JSONDecodeError as e:
        logger.error(f"從 ROI 組態檔案 {config_path} 解碼 JSON 錯誤: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"從 {config_path} 載入 ROI 組態錯誤: {traceback.format_exc()}", exc_info=True)
        return []

# ============================
# [IMPROVED] 幫助函式: 從 CSV 讀取日出日落時間
# ============================
def _read_sun_times_from_csv(csv_file_path):
    sun_times = {}
    if not os.path.exists(csv_file_path):
        logger.warning(f"找不到日出日落時間 CSV 檔案: {csv_file_path}")
        return sun_times
    try:
        with open(csv_file_path, mode='r', newline='', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            # 防禦性地嘗試跳過標頭行
            try:
                first_row = next(csv_reader)
                if not first_row or first_row[0].strip().lower() != 'date':
                    logger.warning(f"{csv_file_path} 中的第一行看起來不像標頭 ('date')，將其視為資料。")
                    csvfile.seek(0) # 倒回開頭重新讀取
                else:
                    logger.debug(f"正在跳過 {csv_file_path} 中的標頭行。")
            except StopIteration:
                logger.warning(f"日出日落時間 CSV 檔案 {csv_file_path} 為空。")
                return sun_times  # 檔案為空

            for row in csv_reader:
                if len(row) >= 3:
                    date_str, sunrise_str, sunset_str = row[:3]
                    try:
                        # 直接使用 'M/D' 格式的日期字串作為鍵
                        normalized_date_str = date_str.strip()
                        sunrise_time = datetime.datetime.strptime(sunrise_str.strip(), '%H:%M').time()
                        sunset_time = datetime.datetime.strptime(sunset_str.strip(), '%H:%M').time()
                        sun_times[normalized_date_str] = (sunrise_time, sunset_time)
                    except ValueError as ve:
                        logger.warning(f"CSV 行 {row} 中的時間或日期格式錯誤: {ve}。跳過此行。")
                    except Exception as row_e:
                        logger.error(f"處理 CSV 行 {row} 錯誤: {row_e}。跳過此行。", exc_info=True)
                else:
                    logger.warning(f"CSV 行格式錯誤 (需要至少3列): {row}。跳過此行。")
        logger.info(f"已從日出日落時間 CSV 檔案 {csv_file_path} 載入 {len(sun_times)} 筆資料")
    except FileNotFoundError:
        logger.warning(f"日出日落時間 CSV 檔案未找到: {csv_file_path}")
    except Exception as e:
        logger.error(f"讀取 CSV 檔案 {csv_file_path} 錯誤: {traceback.format_exc()}", exc_info=True)
    return sun_times

def send_smoke_email(email_config, camera_side, event_time, duration, severity):
    if not email_config.getboolean('send_email_enabled', False):
        return

    api_url = email_config.get('api_url', None)
    mail_to = email_config.get('mail_to', None)
    mail_cc = email_config.get('mail_cc', '')
    subject_template = email_config.get('subject_template', '煙霧偵測警報 {side}')
    body_template = email_config.get('body_template', '攝影機 {side} 偵測到煙霧。')
    system_name = email_config.get('system_name', 'SmokeDetection')
    user_id = email_config.get('user_id', 'system')

    if not api_url or not mail_to:
        logger.error(f"{camera_side} 攝影機: 電子郵件 API URL ({api_url}) 或收件人 (mail_to={mail_to}) 未配置。無法發送電子郵件。")
        return

    try:
        event_datetime_obj = datetime.datetime.fromtimestamp(event_time)
        formatted_subject = subject_template.format(
            side=camera_side.capitalize(),
            time=event_datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
        )
        formatted_body = body_template.format(
            side=camera_side.capitalize(),
            time=event_datetime_obj.strftime('%Y-%m-%d %H:%M:%S'),
            duration=duration,
            severity=severity
        )

        email_data = {
            "body": formatted_body,
            "mail_to": mail_to,
            "mail_cc": mail_cc,
            "subject": formatted_subject,
            "Sys": system_name,
            "Usr": user_id
        }

        payload = json.dumps(email_data)
        headers = {"Content-Type": "application/json"}

        logger.info(f"{camera_side} 攝影機: 嘗試發送煙霧警報電子郵件給 {mail_to} (副本: {mail_cc})...")
        response = requests.post(api_url, headers=headers, data=payload, timeout=15)

        response.raise_for_status()

        logger.info(f"{camera_side} 攝影機: 透過 API 成功發送電子郵件。回應狀態: {response.status_code}。回應內容: {response.text}")

    except requests.exceptions.Timeout:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: 請求在 15 秒後超時。")
    except requests.exceptions.ConnectionError as e:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: 連接 {api_url} 錯誤 - {e}")
    except requests.exceptions.HTTPError as e:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: HTTP 錯誤 - {e} (回應: {getattr(e.response, 'text', 'N/A')})")
    except requests.exceptions.RequestException as e:
        logger.error(f"{camera_side} 攝影機: 電子郵件發送失敗: 一般請求錯誤 - {e}")
    except Exception as e:
        logger.error(f"{camera_side} 攝影機: 發送電子郵件期間發生意外錯誤: {traceback.format_exc()}", exc_info=True)

class VideoPanel(tk.Frame):
    def __init__(self, parent, config, side="left", state=None):
        super().__init__(parent, bg='light green')
        self.config = config
        self.side = side
        self.video_path = self.config.get(f'{self.side.capitalize()}Camera', 'video_path')
        self.roi_config_path = self.config.get(f'{self.side.capitalize()}Camera', 'roi_config_path')
        self.image_save_dir = self.config.get(f'{self.side.capitalize()}Camera', 'image_save_dir')

        self.rois = load_roi_from_config(self.roi_config_path)
        if not self.rois:
            logger.error(f"{self.side} 攝影機: 未從 {self.roi_config_path} 載入有效的 ROI。偵測可能已禁用。")

        self.clf = None

        self.process_interval = self.config.getfloat('Detection', 'process_interval', fallback=1.0)
        self.save_interval_high_prob = self.config.getfloat('Detection', 'save_interval_high_prob', fallback=30.0)
        self.fixed_size = (self.config.getint('Detection', 'fixed_image_width', fallback=128),
                           self.config.getint('Detection', 'fixed_image_height', fallback=128))
        self.smoke_threshold = self.config.getfloat('Detection', 'smoke_threshold', fallback=0.5)
        self.min_event_duration = self.config.getfloat('Detection', 'min_event_duration', fallback=5.0)
        logger.info(f"{self.side} 攝影機: min_event_duration 已設定為 {self.min_event_duration} 秒。")

        self.high_prob_threshold_for_daily_count = self.config.getfloat('Detection', 'high_prob_threshold_for_daily_count', fallback=0.95)
        self.daily_count_interval_seconds = self.config.getfloat('Detection', 'daily_count_interval_minutes', fallback=5.0) * 60

        self.feature_config = self.config['Features']
        self.email_config = self.config['EmailSettings']
        self.classifier_config = self.config['Classifier']

        self.sun_times_csv_path = self.config.get('General', 'sun_times_csv', fallback='sun_date.csv')
        self.default_sunrise = self.config.get('General', 'default_sunrise', fallback='06:00')
        self.default_sunset = self.config.get('General', 'default_sunset', fallback='19:00')
        self.sun_times_data = _read_sun_times_from_csv(self.sun_times_csv_path)
        self.max_reconnect_attempts = self.config.getint('General', 'watchdog_restart_timeout', fallback=60) // 10
        self.watchdog_interval = self.config.getint('General', 'watchdog_interval', fallback=30)

        self.last_frame_time = time.time()
        
        state = state if state is not None else {}
        self.smoke_event_start = state.get("smoke_event_start")
        self.last_counted_time = state.get("last_counted_time", 0)
        self.initial_count_done = state.get("initial_count_done", False)

        self.current_event_start = state.get("current_event_start")
        self.current_event_severity = state.get("current_event_severity", 0.0)

        self.cap_lock = threading.Lock()
        self.cap = None
        self.last_frame = None
        self.frame_lock = threading.Lock()
        self.reconnect_attempts = 0
        self.reconnect_cooldown = 1
        self.active = True
        self.last_detection_status = None
        self.detection_enabled = self.is_detection_time()

        self.last_update = state.get("last_update", 0)
        self.high_prob_last_save_time = state.get("high_prob_last_save_time", 0)
        self.last_email_time = state.get("last_email_time", 0)
        self.min_email_interval = self.email_config.getfloat('min_email_interval', fallback=600.0)
        
        self.smoke_probs_history = deque(state.get("smoke_probs_history", []), maxlen=60)
        self.daily_counts = OrderedDict(state.get("daily_counts", {}))
        heatmap_items = state.get("last7_heatmap", {}).items()
        self.last7_heatmap = OrderedDict(((str(k), np.array(v) if isinstance(v, list) else v) for k, v in heatmap_items))

        self.event_durations = state.get("event_durations", [])
        self.event_severities = state.get("event_severities", [])

        self.heatmap_tracking_date = None
        self.last_half_hour_index = None
        self.heatmap_last_update_time = time.time()

        self.video_canvas = tk.Canvas(self, width=768, height=432, bg='light green', highlightthickness=0)
        self.video_canvas.pack(pady=10)
        self.data_frame = tk.Frame(self, bg='light green')
        self.data_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.stats_label_font = tkFont.Font(family=CHINESE_FONT, size=10)
        self.canvas_text_font = tkFont.Font(family=CHINESE_FONT, size=30)
        self.error_text_font = tkFont.Font(family=CHINESE_FONT, size=20)
        self.system_error_font = tkFont.Font(family=CHINESE_FONT, size=30)
        self.roi_status_font_scale = min(768, 432) / 1500.0
        self.overall_status_font_scale = min(768, 432) / 800.0

        self.stats_label = tk.Label(self.data_frame, text="", bg="light green", font=self.stats_label_font)
        self.stats_label.pack(pady=5)

        self.heatmap_cbar = None

        self._init_charts()

        if self.detection_enabled and self.rois:
            self.setup_stream()
        else:
             logger.warning(f"{self.side} 攝影機: 未設定串流 - 偵測已禁用 ({not self.detection_enabled}) 或未定義 ROI ({not self.rois})。")

        self.video_thread = threading.Thread(target=self.video_capture_thread, daemon=True)
        self.video_thread.start()

        self.watchdog_time = time.time()
        self.after(self.watchdog_interval * 1000, self.watchdog_check)

        self.update_frame()

    def set_classifier(self, clf):
        self.clf = clf
        if clf is not None:
             logger.info(f"{self.side} 攝影機: 分類器已設定。")
        else:
             logger.warning(f"{self.side} 攝影機: 分類器正被設定為 None。")

    def setup_stream(self):
        if not self.detection_enabled:
             logger.debug(f"{self.side} 攝影機: setup_stream 被呼叫但偵測已禁用。")
             return
        if not self.rois:
             logger.warning(f"{self.side} 攝影機: 無法設定串流，未定義 ROI。")
             return

        with self.cap_lock:
            if self.cap is not None:
                try:
                    release_thread = threading.Thread(target=self.cap.release)
                    release_thread.start()
                    release_thread.join(timeout=5)
                    if release_thread.is_alive():
                         logger.warning(f"{self.side} 攝影機: cap.release 超時。資源可能仍被佔用。")
                    else:
                         logger.info(f"{self.side} 攝影機: 在設定前已釋放現有串流。")
                except Exception as e:
                     logger.error(f"{self.side} 攝影機: 嘗試釋放現有串流時發生錯誤: {e}", exc_info=True)
                self.cap = None

            try:
                logger.info(f"{self.side} 攝影機: 嘗試開啟串流: {self.video_path}")
                self.cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                if not self.cap.isOpened():
                    logger.error(f"{self.side} 攝影機: 無法開啟串流 {self.video_path}")
                    raise IOError(f"無法開啟視訊串流 {self.video_path}")

                self.reconnect_attempts = 0
                self.reconnect_cooldown = 1
                logger.info(f"{self.side} 攝影機: 串流設定成功")

            except Exception as e:
                logger.error(f"{self.side} 攝影機: 串流設定錯誤: {traceback.format_exc()}", exc_info=True)
                self.reconnect_attempts += 1
                if self.reconnect_attempts <= self.max_reconnect_attempts:
                     logger.warning(f"{self.side} 攝影機: 將在 {self.reconnect_cooldown} 秒後重新連接 (嘗試 {self.reconnect_attempts}/{self.max_reconnect_attempts})")
                     self.after(int(self.reconnect_cooldown * 1000), self.setup_stream)
                     self.reconnect_cooldown = min(30, self.reconnect_cooldown * 2)
                else:
                     logger.critical(f"{self.side} 攝影機: 串流設定已達到最大重新連接嘗試次數。串流是否永久失敗？")
                     root = self.winfo_toplevel()
                     if hasattr(root, "restart_application"):
                         logger.critical(f"{self.side} 攝影機: 觸發應用程式重新啟動。")
                         root.after(1, root.restart_application)

    def pause_stream(self):
        with self.cap_lock:
            if self.cap is not None:
                try:
                    release_thread = threading.Thread(target=self.cap.release)
                    release_thread.start()
                    release_thread.join(timeout=5)
                    if release_thread.is_alive():
                         logger.warning(f"{self.side} 攝影機: cap.release 在暫停期間超時。資源可能仍被佔用。")
                    else:
                         logger.info(f"{self.side} 攝影機: 串流已暫停 (資源已釋放)")
                except Exception as e:
                    logger.error(f"{self.side} 暫停串流錯誤: {traceback.format_exc()}", exc_info=True)
                finally:
                    self.cap = None
            else:
                 logger.debug(f"{self.side} 攝影機: 串流已暫停或未初始化。")

    def video_capture_thread(self):
        last_frame_read_time = 0
        read_interval = 1.0 / 30.0

        while self.active:
            try:
                current_time = time.time()
                if current_time - last_frame_read_time < read_interval:
                    time.sleep(max(0, read_interval - (current_time - last_frame_read_time)))
                    continue

                if not self.detection_enabled or self.cap is None or not self.cap.isOpened():
                    time.sleep(0.1)
                    continue

                with self.cap_lock:
                    try:
                        ret, frame = self.cap.read()
                    except cv2.error as e:
                        logger.error(f"{self.side} 擷取執行緒 cv2.error 讀取影格: {e}。正在嘗試串流設定。")
                        self.cap = None
                        continue

                if ret and frame is not None:
                    last_frame_read_time = time.time()
                    with self.frame_lock:
                        self.last_frame = frame.copy()
                        self.last_frame_time = last_frame_read_time
                else:
                    logger.debug(f"{self.side} 擷取執行緒: 讀取影格失敗 (ret={ret}, frame 為 None)。")
                    time.sleep(0.5)

            except Exception as e:
                logger.error(f"{self.side} 擷取執行緒未處理的錯誤: {traceback.format_exc()}", exc_info=True)
                time.sleep(1.0)

        logger.info(f"{self.side} 擷取執行緒停止。")

    def is_detection_time(self):
        now = datetime.datetime.now().time()
        today_date = datetime.date.today()
        # Use format_date to get the 'M/D' key for lookup
        today_date_str = format_date(today_date)

        sunrise = None
        sunset = None

        if today_date_str in self.sun_times_data:
            sunrise, sunset = self.sun_times_data[today_date_str]
        else:
            try:
                sunrise = datetime.datetime.strptime(self.default_sunrise, '%H:%M').time()
                sunset = datetime.datetime.strptime(self.default_sunset, '%H:%M').time()
            except ValueError as ve:
                logger.error(f"{self.side}: 組態中預設日出/日落格式無效 '{self.default_sunrise}' 或 '{self.default_sunset}'。假設為 24/7 偵測。錯誤: {ve}", exc_info=True)
                return True

        if sunrise is not None and sunset is not None:
             if sunrise <= sunset:
                 return sunrise <= now <= sunset
             else: # Handles overnight detection periods
                 return now >= sunrise or now <= sunset
        else:
             logger.warning(f"{self.side}: 無法確定日出日落時間 (CSV 或預設)。假設為 24/7 偵測。")
             return True

    def watchdog_check(self):
        current_time = time.time()
        max_frame_staleness = self.config.getint('General', 'watchdog_restart_timeout', fallback=60)

        if self.detection_enabled:
            if current_time - self.last_frame_time > max_frame_staleness:
                logger.warning(f"{self.side} 看門狗: 在 >{max_frame_staleness}s 內未收到新影格 ({current_time - self.last_frame_time:.1f}s 過期)。正在嘗試串流恢復。")
                with self.cap_lock:
                    if self.cap:
                        self.cap.release()
                        self.cap = None
                self.after(1, self.setup_stream)
                self.watchdog_time = current_time
            elif not self.video_thread.is_alive():
                 logger.critical(f"{self.side} 看門狗: 視訊擷取執行緒未啟用！正在嘗試串流設定並記錄崩潰詳細資訊。")
                 with self.cap_lock:
                     if self.cap:
                         self.cap.release()
                         self.cap = None
                 self.after(1, self.setup_stream)
                 self.watchdog_time = current_time
            else:
                 self.watchdog_time = current_time

        if self.winfo_exists():
             self.after(self.watchdog_interval * 1000, self.watchdog_check)
        else:
             logger.info(f"{self.side} 看門狗停止，面板不存在。")

    def _init_charts(self):
        try:
            plt.style.use('ggplot')
            self.curve_fig = Figure(figsize=(7.5, 1.6), dpi=90)
            self.curve_ax = self.curve_fig.add_subplot(111)
            self.curve_line, = self.curve_ax.plot([], [], 'b-')
            self.curve_canvas_tk = FigureCanvasTkAgg(self.curve_fig, master=self.data_frame)
            self.curve_canvas_tk.get_tk_widget().pack(pady=5, fill=tk.BOTH, expand=True)

            self.heatmap_fig = Figure(figsize=(7.5, 1.8), dpi=90)
            self.heatmap_ax = self.heatmap_fig.add_subplot(111)
            self.heatmap_fig.subplots_adjust(left=0.08, right=0.95, top=0.85, bottom=0.15)
            self.heatmap_canvas_tk = FigureCanvasTkAgg(self.heatmap_fig, master=self.data_frame)
            self.heatmap_canvas_tk.get_tk_widget().pack(pady=1, fill=tk.BOTH, expand=True)
            self.heatmap_im = None

            self.bar_fig = Figure(figsize=(7.5, 1.6), dpi=90)
            self.bar_ax = self.bar_fig.add_subplot(111)
            self.bar_canvas_tk = FigureCanvasTkAgg(self.bar_fig, master=self.data_frame)
            self.bar_canvas_tk.get_tk_widget().pack(pady=5, fill=tk.BOTH, expand=True)

            self.update_charts()
        except Exception as e:
             logger.error(f"{self.side} 攝影機: 初始化圖表失敗: {traceback.format_exc()}", exc_info=True)

    def update_frame(self):
        if not self.winfo_exists():
            logger.info(f"{self.side} 面板不再存在，停止更新")
            self.cleanup()
            return

        try:
            now_dt = datetime.datetime.now()
            current_time_sec = now_dt.timestamp()
            current_detection_enabled = self.is_detection_time()

            if current_detection_enabled != self.detection_enabled:
                self.detection_enabled = current_detection_enabled
                logger.info(f"{self.side}: 偵測狀態變更為 {self.detection_enabled}")
                if self.detection_enabled:
                    if self.rois:
                         self.setup_stream()
                    else:
                         logger.warning(f"{self.side} 攝影機: 偵測已啟用但無 ROI，未設定串流。")
                else:
                    self.pause_stream()
                    self.video_canvas.delete("all")
                    self.video_canvas.create_text(768//2, 432//2, text="非偵測期間",
                                              font=self.canvas_text_font, fill="red")
                    self.after(1000, self.update_frame)
                    return

            if not self.detection_enabled:
                 self.after(1000, self.update_frame)
                 return

            frame = None
            with self.frame_lock:
                if self.last_frame is not None:
                    frame = self.last_frame.copy()
                frame_staleness = current_time_sec - self.last_frame_time if self.last_frame is not None else float('inf')

            if frame is None or frame_staleness > 5:
                 self.video_canvas.delete("all")
                 self.video_canvas.create_text(768//2, 432//2, text="無視訊訊號",
                                               font=self.canvas_text_font, fill="red")
                 self.after(100, self.update_frame)
                 return

            if current_time_sec - self.last_update >= self.process_interval:
                self.last_update = current_time_sec
                self.watchdog_time = current_time_sec

                overall_label = "無煙霧"
                new_preds = [(0, 0.0)] * len(self.rois) if self.rois else []
                current_max_smoke_prob = 0.0
                is_high_prob_smoke_now = False

                if self.clf is not None and self.rois:
                    overall_label, new_preds = predict_image_multiple_rois(
                        frame, self.rois, self.clf, self.feature_config, self.config['Detection'], self.classifier_config
                    )
                    self.last_predictions = new_preds

                    smoke_roi_probs = [prob for (pred, prob) in new_preds if pred == 1]
                    current_max_smoke_prob = max(smoke_roi_probs) if smoke_roi_probs else 0.0
                    logger.debug(f"{self.side} 攝影機: 處理影格。最大煙霧機率: {current_max_smoke_prob:.2f}")

                    is_high_prob_smoke_now = current_max_smoke_prob >= self.high_prob_threshold_for_daily_count
                else:
                    logger.debug(f"{self.side} 攝影機: 分類器或 ROI 不可用。已跳過此間隔的偵測處理。")

                is_smoke_now = current_max_smoke_prob >= self.smoke_threshold

                self.smoke_probs_history.append((current_time_sec, current_max_smoke_prob))

                today_str = now_dt.strftime("%Y-%m-%d")

                if is_high_prob_smoke_now:
                    if self.smoke_event_start is None:
                        logger.debug(f"{self.side} 攝影機: 高機率煙霧事件於 {datetime.datetime.fromtimestamp(current_time_sec).strftime('%Y-%m-%d %H:%M:%S')} 開始。")
                        self.smoke_event_start = current_time_sec
                        self.last_counted_time = current_time_sec
                        self.initial_count_done = False

                    event_duration_since_start = current_time_sec - self.smoke_event_start

                    if (event_duration_since_start >= self.min_event_duration) and (not self.initial_count_done):
                        logger.info(f"{self.side} 攝影機: 高機率煙霧事件持續時間達到 {self.min_event_duration}s。事件於 {datetime.datetime.fromtimestamp(self.smoke_event_start).strftime('%Y-%m-%d %H:%M:%S')} 開始，觸發首次計數。")

                        if today_str not in self.daily_counts:
                            self.daily_counts[today_str] = 0
                        self.daily_counts[today_str] += 1
                        self.initial_count_done = True
                        self.last_counted_time = current_time_sec
                        logger.info(f"{self.side} 攝影機: 每日煙霧事件計數已增加至 {self.daily_counts[today_str]} (首次計數)。")

                        if self.email_config.getboolean('send_email_enabled', False):
                            if current_time_sec - self.last_email_time >= self.min_email_interval:
                                logger.info(f"{self.side} 攝影機: 符合最小事件持續時間且符合最小郵件間隔。正在觸發郵件。")
                                email_thread = threading.Thread(
                                    target=send_smoke_email,
                                    args=(
                                        self.email_config,
                                        self.side,
                                        self.smoke_event_start,
                                        event_duration_since_start,
                                        current_max_smoke_prob
                                    ),
                                    daemon=True
                                )
                                email_thread.start()
                                self.last_email_time = current_time_sec
                            else:
                                logger.debug(f"{self.side} 攝影機: 符合事件持續時間，但未達到最小郵件間隔。")

                    elif self.initial_count_done and (current_time_sec - self.last_counted_time >= self.daily_count_interval_seconds):
                        logger.info(f"{self.side} 攝影機: 高機率煙霧事件持續中，已達到 {self.daily_count_interval_seconds/60:.0f} 分鐘間隔，再次計數。")
                        if today_str not in self.daily_counts:
                            self.daily_counts[today_str] = 0
                        self.daily_counts[today_str] += 1
                        self.last_counted_time = current_time_sec
                        logger.info(f"{self.side} 攝影機: 每日煙霧事件計數已增加至 {self.daily_counts[today_str]} (重複計數)。")

                    if self.current_event_start is None:
                        self.current_event_start = current_time_sec
                        self.current_event_severity = current_max_smoke_prob
                    else:
                        duration_since_current_event_start = current_time_sec - self.current_event_start
                        if duration_since_current_event_start > 0:
                            self.current_event_severity = (self.current_event_severity * (duration_since_current_event_start - self.process_interval) + current_max_smoke_prob * self.process_interval) / duration_since_current_event_start
                            if self.current_event_severity < 0 or np.isnan(self.current_event_severity):
                                self.current_event_severity = current_max_smoke_prob
                        else:
                            self.current_event_severity = current_max_smoke_prob

                else:
                    if self.smoke_event_start is not None:
                         event_duration_actual = current_time_sec - self.smoke_event_start
                         if event_duration_actual >= self.min_event_duration:
                             if self.current_event_start is not None:
                                  final_event_duration = current_time_sec - self.current_event_start
                                  final_avg_severity = self.current_event_severity
                                  self.event_durations.append(final_event_duration)
                                  self.event_severities.append(final_avg_severity)
                                  logger.info(f"{self.side} 攝影機: 煙霧事件結束。持續時間: {final_event_duration:.1f}s，最終平均嚴重性: {final_avg_severity:.2f}")
                             else:
                                logger.warning(f"{self.side} 攝影機: 事件結束但 current_event_start 為 None。")
                         else:
                              logger.debug(f"{self.side} 攝影機: 潛在事件太短 ({event_duration_actual:.1f}s)，未計數。")

                    self.smoke_event_start = None
                    self.last_counted_time = 0
                    self.initial_count_done = False
                    self.current_event_start = None
                    self.current_event_severity = 0.0

                today_heatmap_str = now_dt.strftime("%Y-%m-%d")
                if today_heatmap_str not in self.last7_heatmap:
                    self.last7_heatmap[today_heatmap_str] = np.zeros(48, dtype=int)

                half_hour_idx = (now_dt.hour * 60 + now_dt.minute) // 30

                if is_smoke_now:
                    if 0 <= half_hour_idx < 48:
                        self.last7_heatmap[today_heatmap_str][half_hour_idx] += 1
                    else:
                        logger.warning(f"{self.side} 攝影機: 熱力圖半小時索引 {half_hour_idx} 超出範圍 [0, 47]。跳過熱力圖更新。")

                sorted_heatmap_dates = sorted(self.last7_heatmap.keys())
                while len(sorted_heatmap_dates) > 7:
                     oldest_heatmap_date = sorted_heatmap_dates.pop(0)
                     if oldest_heatmap_date in self.last7_heatmap:
                         del self.last7_heatmap[oldest_heatmap_date]
                         logger.debug(f"{self.side}: 移除最舊的熱力圖資料 {oldest_heatmap_date}。")

                sorted_daily_dates = sorted(self.daily_counts.keys())
                while len(sorted_daily_dates) > 7:
                    oldest_daily_date = sorted_daily_dates.pop(0)
                    if oldest_daily_date in self.daily_counts:
                        del self.daily_counts[oldest_daily_date]
                        logger.debug(f"{self.side}: 移除最舊的每日計數 {oldest_daily_date}。")
                
                if current_time_sec // 3 != (current_time_sec - self.process_interval) // 3 or \
                   (current_time_sec - self.heatmap_last_update_time) > 5:
                    logger.debug(f"{self.side} 攝影機: 觸發圖表更新。")
                    self.update_charts()
                    self.heatmap_last_update_time = current_time_sec

                if current_max_smoke_prob > 0.9:
                    if current_time_sec - self.high_prob_last_save_time >= self.save_interval_high_prob:
                        try:
                            os.makedirs(self.image_save_dir, exist_ok=True)
                            filename = time.strftime("%Y%m%d_%H%M%S.jpg", time.localtime())
                            save_path = os.path.join(self.image_save_dir, filename)
                            cv2.imwrite(save_path, frame.copy())
                            logger.info(f"{self.side}: 已儲存高機率煙霧原始影像: {save_path}")
                            self.high_prob_last_save_time = current_time_sec

                        except Exception as e:
                            logger.error(f"{self.side} 影像儲存錯誤: {traceback.format_exc()}", exc_info=True)


            if frame is not None and hasattr(self, 'last_predictions') and self.last_predictions is not None and self.rois:
                 overlayed_frame = self._draw_detection_overlay_with_chinese(frame.copy(), self.last_predictions, self.rois, self.smoke_threshold)
            else:
                 overlayed_frame = frame
                 if overlayed_frame is not None:
                     status_text = "等待偵測資料..."
                     if not self.rois:
                         status_text = "未定義 ROI！"
                     pil_img = Image.fromarray(cv2.cvtColor(overlayed_frame, cv2.COLOR_BGR2RGB))
                     draw = ImageDraw.Draw(pil_img)
                     if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
                         try:
                             font_size = int(self.overall_status_font_scale * 50)
                             chinese_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, font_size)
                         except IOError:
                             chinese_font_pil = ImageFont.load_default()
                     else:
                         chinese_font_pil = ImageFont.load_default()

                     draw.text((10, pil_img.height - font_size - 10), status_text, font=chinese_font_pil, fill=(0, 0, 255))
                     overlayed_frame = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
                     logger.debug(f"{self.side} 攝影機: 無有效 ROI 或預測資料可繪製。")

            if overlayed_frame is not None:
                try:
                    if len(overlayed_frame.shape) == 2:
                         frame_bgr = cv2.cvtColor(overlayed_frame, cv2.COLOR_GRAY2BGR)
                    elif overlayed_frame.shape[2] == 4:
                         frame_bgr = cv2.cvtColor(overlayed_frame, cv2.COLOR_BGRA2BGR)
                    else:
                         frame_bgr = overlayed_frame

                    frame_resized = cv2.resize(frame_bgr, (768, 432))
                    frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                    image_pil = Image.fromarray(frame_rgb)
                    photo = ImageTk.PhotoImage(image=image_pil)

                    self.video_canvas.delete("all")
                    self.video_canvas.photo = photo
                    self.video_canvas.create_image(0, 0, image=photo, anchor=tk.NW)
                except Exception as e:
                    logger.error(f"{self.side} 顯示影格錯誤: {traceback.format_exc()}", exc_info=True)
                    self.video_canvas.delete("all")
                    self.video_canvas.create_text(768//2, 432//2, text=f"顯示錯誤:\n{e}",
                                                  font=self.error_text_font, fill="red")
            else:
                 self.video_canvas.delete("all")
                 self.video_canvas.create_text(768//2, 432//2, text="無視訊訊號",
                                               font=self.canvas_text_font, fill="red")

        except Exception as e:
            logger.error(f"{self.side} 影格更新錯誤: {traceback.format_exc()}", exc_info=True)
            self.video_canvas.delete("all")
            self.video_canvas.create_text(768//2, 432//2, text=f"系統錯誤\n檢查日誌",
                                          font=self.system_error_font, fill="red")
            self.after(2000, self.update_frame)

        self.after(50, self.update_frame)

    def _draw_detection_overlay_with_chinese(self, frame, predictions, rois, smoke_threshold):
        if frame is None:
            return frame
        if predictions is None or not rois or len(predictions) != len(rois):
            logger.warning(f"{self.side}: _draw_detection_overlay_with_chinese called with invalid predictions or ROIs.")
            pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_img)
            
            if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
                try:
                    font_size = int(self.overall_status_font_scale * 50)
                    chinese_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, font_size)
                except IOError:
                    logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                    chinese_font_pil = ImageFont.load_default()
            else:
                chinese_font_pil = ImageFont.load_default()
            
            draw.text((10, pil_img.height - font_size - 10), "等待偵測資料...", font=chinese_font_pil, fill=(0, 0, 255))
            return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)

        if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
            try:
                overall_font_size = int(self.overall_status_font_scale * 150)
                chinese_overall_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, overall_font_size)
            except IOError:
                logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                chinese_overall_font_pil = ImageFont.load_default()
        else:
            chinese_overall_font_pil = ImageFont.load_default()

        overall_smoke_detected = any(pred == 1 for (pred, _) in predictions)
        smoke_probs = [prob for (pred, prob) in predictions if pred == 1]
        overall_max_prob = max(smoke_probs) if smoke_probs else 0.0

        overall_alert_color_rgb = (255, 0, 0) if overall_max_prob > 0.9 else (255, 165, 0)
        if overall_smoke_detected:
            status_text = f"煙霧警報 ({overall_max_prob:.2f})"
            text_color_rgb = overall_alert_color_rgb
        else:
            status_text = "無煙霧"
            text_color_rgb = (0, 255, 0)

        text_origin_x = 10
        text_origin_y = 10
        draw.text((text_origin_x, text_origin_y), status_text, font=chinese_overall_font_pil, fill=text_color_rgb)

        if FONT_PATH_FOR_OPENCV and os.path.exists(FONT_PATH_FOR_OPENCV):
            try:
                roi_font_size = int(self.roi_status_font_scale * 150)
                chinese_roi_font_pil = ImageFont.truetype(FONT_PATH_FOR_OPENCV, roi_font_size)
            except IOError:
                logger.warning(f"無法加載字體文件: {FONT_PATH_FOR_OPENCV}。將使用PIL預設字體。")
                chinese_roi_font_pil = ImageFont.load_default()
        else:
            chinese_roi_font_pil = ImageFont.load_default()

        for i, roi in enumerate(rois):
            if i < len(predictions):
                pred, prob = predictions[i]
            else:
                pred, prob = (0, 0.0)

            roi_color_rgb = (255, 0, 0) if pred == 1 else (0, 255, 0)

            pil_roi_points = [coord for point in roi for coord in point]
            draw.polygon(pil_roi_points, outline=roi_color_rgb, width=5)

            roi_status_text = f"{prob:.2f}"
            x_min = np.min(np.array(roi)[:, 0])
            y_min = np.min(np.array(roi)[:, 1])
            text_pos_roi = (x_min - 80, y_min + 90)
            draw.text(text_pos_roi, roi_status_text, font=chinese_roi_font_pil, fill=roi_color_rgb)

        return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

    def update_curve_plot(self):
        try:
            self.curve_ax.clear()
            self.curve_ax.set_title('即時煙霧機率')
            self.curve_ax.set_xlabel('時間 (秒)')
            self.curve_ax.set_ylabel('煙霧機率')
            self.curve_ax.set_ylim(0, 1.1)
            self.curve_ax.axhline(self.smoke_threshold, color='r', linestyle='--', label=f'閾值 ({self.smoke_threshold:.2f})')

            if self.smoke_probs_history:
                valid_history = [p for p in self.smoke_probs_history if isinstance(p, tuple) and len(p) == 2]
                if not valid_history:
                    logger.warning(f"{self.side} 曲線圖: 煙霧機率歷史紀錄為空或無效。")
                    self.curve_ax.legend(loc='upper right', fontsize=8)
                    self.curve_fig.tight_layout()
                    self.curve_canvas_tk.draw_idle()
                    return

                times, probs = zip(*valid_history)
                start_time = times[0]
                rel_times = [t - start_time for t in times]

                self.curve_ax.plot(rel_times, probs, 'b-', label='最大 ROI 機率')
                self.curve_ax.legend(loc='upper right', fontsize=8)

                event_segments = []
                segment_start_idx = None
                for i in range(len(probs)):
                     if probs[i] >= self.smoke_threshold:
                          if segment_start_idx is None:
                               segment_start_idx = i
                     else:
                          if segment_start_idx is not None:
                               event_segments.append((segment_start_idx, i))
                               segment_start_idx = None
                if segment_start_idx is not None:
                     event_segments.append((segment_start_idx, len(probs)))

                for start_idx, end_idx in event_segments:
                     if start_idx < len(rel_times):
                          start_time_shade = rel_times[start_idx]
                          if end_idx < len(rel_times):
                              end_time_shade = rel_times[end_idx-1] + self.process_interval if end_idx > 0 else rel_times[start_idx] + self.process_interval
                          else:
                              end_time_shade = rel_times[-1] + self.process_interval

                          self.curve_ax.axvspan(start_time_shade, end_time_shade,
                                               color='red', alpha=0.2)

                if rel_times:
                    x_max = rel_times[-1] if len(rel_times) > 1 else self.process_interval
                    self.curve_ax.set_xlim(0, x_max + self.process_interval)

            self.curve_ax.set_xticklabels([])

            self.curve_fig.tight_layout()
            self.curve_canvas_tk.draw_idle()

        except Exception as e:
            logger.error(f"{self.side} 曲線圖錯誤: {traceback.format_exc()}", exc_info=True)

    def update_heatmap_display(self):
        try:
            self.heatmap_ax.clear()
            self.heatmap_ax.set_title('熱力圖 (最近 7 天)', fontsize=10)
            self.heatmap_ax.set_xlabel('一天中的小時', fontsize=8)
            self.heatmap_ax.set_ylabel('日期', fontsize=8)

            today = datetime.date.today()
            dates = [(today - datetime.timedelta(days=i)) for i in range(6, -1, -1)]
            date_strs = [d.strftime("%Y-%m-%d") for d in dates]

            matrix = []
            for ds in date_strs:
                day_data = self.last7_heatmap.get(ds, np.zeros(48, dtype=int))
                if isinstance(day_data, list):
                     day_data = np.array(day_data, dtype=int)

                if isinstance(day_data, np.ndarray) and day_data.shape == (48,):
                     matrix.append(day_data)
                else:
                     logger.warning(f"{ds} 的熱力圖資料格式不符合預期 ({type(day_data)}, 形狀 {getattr(day_data, 'shape', 'N/A')})。正在重置此日期的資料。")
                     matrix.append(np.zeros(48, dtype=int))
                     self.last7_heatmap[ds] = np.zeros(48, dtype=int)

            matrix = np.array(matrix, dtype=int)

            if matrix.shape != (7, 48):
                 logger.error(f"{self.side} 熱力圖矩陣形狀錯誤: {matrix.shape}。預期為 (7, 48)。無法顯示。", exc_info=True)
                 self.heatmap_ax.text(0.5, 0.5, "熱力圖資料錯誤", horizontalalignment='center', verticalalignment='center', transform=self.heatmap_ax.transAxes, color='red', fontdict={'family':CHINESE_FONT})
                 self.heatmap_fig.canvas.draw_idle()
                 return

            cmap = plt.get_cmap('YlOrRd')

            max_val_matrix = np.max(matrix) if np.any(matrix) else 1
            fixed_vmax = 60
            actual_vmax = max(max_val_matrix, fixed_vmax)


            self.heatmap_im = self.heatmap_ax.imshow(matrix, cmap=cmap, aspect='auto', interpolation='nearest', vmin=0, vmax=actual_vmax)

            tick_positions = np.arange(0.5, 48.5, 2)
            tick_labels = [f"{i:02d}" for i in range(24)]
            self.heatmap_ax.set_xticks(tick_positions)
            self.heatmap_ax.set_xticklabels(tick_labels, fontsize=7)

            self.heatmap_ax.set_yticks(range(len(date_strs)))
            date_labels_formatted = [format_date(d) for d in dates]
            self.heatmap_ax.set_yticklabels(date_labels_formatted, fontsize=7)

            if self.heatmap_cbar is None:
                if self.heatmap_im:
                    self.heatmap_cbar = self.heatmap_fig.colorbar(self.heatmap_im, ax=self.heatmap_ax, orientation='vertical', fraction=0.02, pad=0.04, label=f'(最大 {actual_vmax})')
            else:
                self.heatmap_cbar.update_normal(self.heatmap_im)
                self.heatmap_cbar.set_label(f'(最大 {actual_vmax})')


            self.heatmap_fig.tight_layout()
            self.heatmap_fig.canvas.draw_idle()

        except Exception as e:
            logger.error(f"{self.side} 熱力圖繪製錯誤: {traceback.format_exc()}", exc_info=True)

    def update_bar_chart(self):
        try:
            self.bar_ax.clear()
            self.bar_ax.set_title('每日煙霧事件 (最近 7 天)')
            self.bar_ax.set_ylabel("煙霧事件")

            today = datetime.date.today()
            dates = [(today - datetime.timedelta(days=i)) for i in range(6, -1, -1)]
            date_strs = [d.strftime("%Y-%m-%d") for d in dates]

            counts = [self.daily_counts.get(ds, 0) for ds in date_strs]

            bars = self.bar_ax.bar(range(len(date_strs)), counts, color='skyblue')

            for i, count in enumerate(counts):
                if count > 0:
                    self.bar_ax.text(i, count + 0.1, str(count), ha='center', va='bottom', fontsize=9)

            self.bar_ax.set_xticks(range(len(date_strs)))
            x_labels = [format_date(d) for d in dates]
            self.bar_ax.set_xticklabels(x_labels, rotation=0, fontsize=8)

            self.bar_ax.spines['top'].set_visible(False)
            self.bar_ax.spines['right'].set_visible(False)
            self.bar_ax.set_ylim(0, max(counts) * 1.2 if max(counts) > 0 else 1)

            self.bar_fig.tight_layout()
            self.bar_canvas_tk.draw_idle()

        except Exception as e:
            logger.error(f"{self.side} 長條圖錯誤: {traceback.format_exc()}", exc_info=True)

    def update_stats_display(self):
        event_count = len(self.event_durations)
        avg_duration = sum(self.event_durations) / event_count if event_count > 0 else 0
        
        avg_severity_completed = sum(self.event_severities) / event_count if event_count > 0 else 0

        total_events_7d = sum(self.daily_counts.values())

        stats_text = (
            f"事件 (7天): {total_events_7d} | "
            f"已完成事件: {event_count} | \n"
            f"平均持續: {avg_duration:.1f} 秒 | "
            f"平均嚴重度 (完成): {avg_severity_completed:.2f}"
        )
        if self.current_event_start is not None:
             current_duration = time.time() - self.current_event_start
             stats_text += f" | 當前事件: {current_duration:.1f} 秒 (平均嚴重度: {self.current_event_severity:.2f})"
             if current_duration >= self.min_event_duration:
                 stats_text += " (符合持續時間)"

        self.stats_label.config(text=stats_text)

    def update_charts(self):
        try:
            self.update_curve_plot()
            self.update_heatmap_display()
            self.update_bar_chart()
            self.update_stats_display()
            gc.collect()
        except Exception as e:
            logger.error(f"{self.side} 整體圖表更新錯誤: {traceback.format_exc()}", exc_info=True)

    def get_state(self):
        try:
            state = {}
            state["smoke_probs_history"] = list(self.smoke_probs_history)
            state["daily_counts"] = list(self.daily_counts.items())
            state["last7_heatmap"] = [(k, v.tolist() if isinstance(v, np.ndarray) else v) for k, v in self.last7_heatmap.items()]

            state["event_durations"] = self.event_durations
            state["event_severities"] = self.event_severities
            state["current_event_start"] = self.current_event_start
            state["current_event_severity"] = self.current_event_severity
            state["smoke_event_start"] = self.smoke_event_start
            state["last_counted_time"] = self.last_counted_time
            state["initial_count_done"] = self.initial_count_done

            state["last_update"] = self.last_update
            state["high_prob_last_save_time"] = self.high_prob_last_save_time
            state["last_email_time"] = self.last_email_time

            return state
        except Exception as e:
            logger.error(f"{self.side} 獲取狀態錯誤: {traceback.format_exc()}", exc_info=True)
            return {}

    def cleanup(self):
        logger.info(f"{self.side}: 正在清理資源")
        self.active = False

        if hasattr(self, "video_thread") and self.video_thread.is_alive():
            logger.info(f"{self.side}: 正在等待視訊執行緒...")
            try:
                self.video_thread.join(timeout=2)
                if self.video_thread.is_alive():
                     logger.warning(f"{self.side}: 視訊執行緒在超時後未正常結束。")
            except Exception as e:
                logger.error(f"{self.side}: 加入視訊執行緒錯誤: {traceback.format_exc()}", exc_info=True)

        self.pause_stream()

        try:
            if hasattr(self, 'curve_fig') and self.curve_fig is not None:
                plt.close(self.curve_fig)
                self.curve_fig = None
            if hasattr(self, 'heatmap_fig') and self.heatmap_fig is not None:
                 plt.close(self.heatmap_fig)
                 self.heatmap_fig = None
            if hasattr(self, 'bar_fig') and self.bar_fig is not None:
                 plt.close(self.bar_fig)
                 self.bar_fig = None
        except Exception as e:
            logger.error(f"{self.side} 攝影機: 關閉 matplotlib 圖形錯誤: {traceback.format_exc()}", exc_info=True)

        gc.collect()
        logger.info(f"{self.side}: 清理完成")

class SmokeDetectionUI(tk.Tk):
    def __init__(self, config, left_clf, right_clf):
        super().__init__()
        self.app_config = config
        self.left_clf = left_clf
        self.right_clf = right_clf

        self.title("煙霧偵測系統")
        self.geometry("1550x1000")
        self.resizable(True, True)
        self.protocol("WM_DELETE_WINDOW", self.on_exit)
        self.bind("<Escape>", lambda e: self.on_exit())

        self._setup_ui_config()

        self.state_file = self.app_config.get('General', 'state_file', fallback="smoke_state.npz")
        self.saved_state = self._load_state()

        self._init_panels()

        self.auto_save_interval_ms = 300000
        if self.winfo_exists():
            self.after(self.auto_save_interval_ms, self.auto_save_state)
        else:
             logger.warning("UI 視窗不存在，跳過初始自動儲存安排。")

        self.last_alive_check = time.time()
        self.ui_watchdog_interval_ms = 30000
        if self.winfo_exists():
            self.after(self.ui_watchdog_interval_ms, self.check_alive)

    def _setup_ui_config(self):
        self.configure(bg='light gray')
        style = ttk.Style()
        style.theme_use('clam')

        try:
            default_font = tkFont.nametofont("TkDefaultFont")
            default_font.configure(family=CHINESE_FONT, size=9)
            tkFont.nametofont("TkTextFont").configure(family=CHINESE_FONT, size=9)
            tkFont.nametofont("TkMenuFont").configure(family=CHINESE_FONT, size=9)
            tkFont.nametofont("TkHeadingFont").configure(family=CHINESE_FONT, size=10, weight="bold")
            tkFont.nametofont("TkFixedFont").configure(family=CHINESE_FONT, size=9)

        except tk.TclError:
            logger.warning(f"設定 Tkinter 字體失敗，可能正在無頭環境中運行。")
        except Exception as e:
            logger.warning(f"設定 Tkinter 預設字體時發生意外錯誤: {e}", exc_info=True)

        menu_bar = tk.Menu(self)
        self.config(menu=menu_bar)

        file_menu = tk.Menu(menu_bar, tearoff=0)
        file_menu.add_command(label="儲存狀態", command=self.save_state)
        file_menu.add_separator()
        file_menu.add_command(label="離開", command=self.on_exit)
        menu_bar.add_cascade(label="檔案", menu=file_menu)

        help_menu = tk.Menu(menu_bar, tearoff=0)
        help_menu.add_command(label="關於", command=self.show_about)
        menu_bar.add_cascade(label="幫助", menu=help_menu)

    def _load_state(self):
        if os.path.exists(self.state_file):
            try:
                npzfile = np.load(self.state_file, allow_pickle=True)
                saved_state = {}
                for key in npzfile.keys():
                    item = npzfile[key]
                    if isinstance(item, np.ndarray) and item.ndim == 0 and item.dtype == object:
                        saved_state[key] = item.item()
                    else:
                        saved_state[key] = item

                for panel_key in ['left', 'right']:
                    if panel_key in saved_state and isinstance(saved_state[panel_key], dict):
                        panel_state_dict = saved_state[panel_key]
                        if 'daily_counts' in panel_state_dict and isinstance(panel_state_dict['daily_counts'], list):
                            panel_state_dict['daily_counts'] = OrderedDict(panel_state_dict['daily_counts'])
                        if 'last7_heatmap' in panel_state_dict and isinstance(panel_state_dict['last7_heatmap'], list):
                            panel_state_dict['last7_heatmap'] = OrderedDict(
                                (k, np.array(v) if isinstance(v, list) else v) for k, v in panel_state_dict['last7_heatmap']
                            )
                        if 'smoke_probs_history' in panel_state_dict and isinstance(panel_state_dict['smoke_probs_history'], list):
                            panel_state_dict['smoke_probs_history'] = deque(panel_state_dict['smoke_probs_history'], maxlen=60)
                        saved_state[panel_key] = panel_state_dict

                logger.info(f"已從 {self.state_file} 載入狀態")
                npzfile.close()
                return saved_state
            except FileNotFoundError:
                logger.info(f"狀態檔案未找到於 {self.state_file}。")
                return {}
            except Exception as e:
                logger.error(f"從 {self.state_file} 載入狀態錯誤: {traceback.format_exc()}", exc_info=True)
                messagebox.showerror("狀態載入錯誤", f"載入狀態檔案 {self.state_file} 錯誤:\n{e}\n以全新狀態啟動。", icon='error')
                return {}
        else:
            logger.info(f"狀態檔案未找到於 {self.state_file}。以全新狀態啟動。")
            return {}

    def _init_panels(self):
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=1)

        try:
            left_state = self.saved_state.get("left", {})
            right_state = self.saved_state.get("right", {})
        except KeyError as e:
            logger.error(f"載入面板狀態錯誤: {e}。使用預設狀態。", exc_info=True)
            left_state = {}
            right_state = {}

        self.left_panel = VideoPanel(self.main_frame, self.app_config, side="left", state=left_state)
        self.right_panel = VideoPanel(self.main_frame, self.app_config, side="right", state=right_state)

        if self.left_clf:
             self.left_panel.set_classifier(self.left_clf)
        else:
             logger.warning("左側分類器為 None，左側面板將不執行偵測。")
             self.left_panel.set_classifier(None)

        if self.right_clf:
             self.right_panel.set_classifier(self.right_clf)
        else:
             logger.warning("右側分類器為 None，右側面板將不執行偵測。")
             self.right_panel.set_classifier(None)

        self.left_panel.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        self.right_panel.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

    def save_state(self):
        try:
            state = {
                "left": self.left_panel.get_state(),
                "right": self.right_panel.get_state()
            }

            save_path = self.state_file
            np.savez_compressed(save_path, **state)
            logger.info(f"狀態已儲存到 {save_path}")

        except Exception as e:
            logger.error(f"儲存狀態到 {self.state_file} 錯誤: {traceback.format_exc()}", exc_info=True)
            messagebox.showerror("儲存錯誤", f"無法儲存狀態: {e}", icon='error')

    def auto_save_state(self):
        if self.winfo_exists():
            try:
                self.save_state()
            except Exception as e:
                logger.error(f"自動儲存期間發生錯誤: {e}", exc_info=True)

            if self.winfo_exists():
                 self.after(self.auto_save_interval_ms, self.auto_save_state)
            else:
                 logger.info("UI 視窗在自動儲存重新安排期間關閉。")

        else:
             logger.info("UI 視窗已關閉，停止自動儲存。")

    def check_alive(self):
        current_time = time.time()
        if current_time - self.last_alive_check > (self.ui_watchdog_interval_ms / 1000.0) * 2:
            logger.warning(f"UI 看門狗: 主 UI 執行緒似乎已停止。上次檢查是 {current_time - self.last_alive_check:.1f} 秒前。如果此情況頻繁發生，請考慮增加 ui_watchdog_interval_ms。")

        self.last_alive_check = current_time
        if self.winfo_exists():
             self.after(self.ui_watchdog_interval_ms, self.check_alive)
        else:
             logger.info("UI 看門狗停止，視窗不存在。")

    def show_about(self):
        messagebox.showinfo(
            "關於煙霧偵測系統",
            "煙霧偵測系統\n版本 2.1 (已修復並整合)\n\n© 2025 貴公司\n版權所有。",
            icon='info'
        )

    def on_exit(self):
        logger.info("應用程式正在關閉...")
        try:
            self.save_state()
            self.left_panel.cleanup()
            self.right_panel.cleanup()
            time.sleep(0.5)
            self.destroy()
            logger.info("應用程式關閉完成。")
        except Exception as e:
            logger.error(f"關閉期間發生錯誤: {traceback.format_exc()}", exc_info=True)
            try:
                self.destroy()
            except Exception as destroy_e:
                 logger.critical(f"銷毀 UI 視窗錯誤: {destroy_e}", exc_info=True)

    def restart_application(self):
        logger.info("正在重新啟動應用程式...")
        try:
            self.save_state()
            self.left_panel.cleanup()
            self.right_panel.cleanup()
            time.sleep(0.5)
            logger.info(f"正在執行: {sys.executable} {' '.join(sys.argv)}")
            os.execl(sys.executable, sys.executable, *sys.argv)
        except Exception as e:
            logger.critical(f"重新啟動應用程式失敗: {traceback.format_exc()}", exc_info=True)
            if hasattr(self, 'after'):
                self.after(1, lambda: messagebox.showerror("重新啟動錯誤", f"重新啟動應用程式失敗: {e}\n正在離開。", icon='error'))
                self.after(2000, self.destroy)
            else:
                 print(f"嚴重重新啟動錯誤: {e}", file=sys.stderr)
                 sys.exit(1)

class ConfigEditorApp(QMainWindow):
    def __init__(self, org_config_file="config_org.ini", current_config_file="config.ini"):
        super().__init__()
        self.org_config_file = org_config_file
        self.current_config_file = current_config_file
        
        self.config_template = configparser.ConfigParser()
        self.config_current = configparser.ConfigParser()

        self.widgets = {} 
        self.comments = {} 

        self.setWindowTitle("Config 參數編輯器")
        self.setGeometry(100, 100, 800, 600)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("初始化中...")

        self._create_ui()
        self._load_config()

    def _create_ui(self):
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        self.main_layout.addWidget(scroll_area)

        scroll_content = QWidget()
        scroll_area.setWidget(scroll_content)
        self.config_layout = QVBoxLayout(scroll_content)
        self.config_layout.setAlignment(Qt.AlignTop)

        button_layout = QVBoxLayout() 
        self.save_button = QPushButton("儲存設定")
        self.save_button.clicked.connect(self._save_config)
        button_layout.addWidget(self.save_button)

        info_label = QLabel(f"註解來自 '{self.org_config_file}'。儲存時，只有參數值會寫入 '{self.current_config_file}'，不含註解和空行。")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: gray; font-size: 9pt;")
        button_layout.addWidget(info_label)

        self.main_layout.addLayout(button_layout)

    def _parse_template_config(self):
        self.config_template = configparser.ConfigParser()
        self.comments = {}
        
        current_section = None
        pending_comments = []

        try:
            with open(self.org_config_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    stripped_line = line.strip()

                    if not stripped_line:
                        pending_comments = []
                        continue
                    
                    if stripped_line.startswith('[') and stripped_line.endswith(']'):
                        current_section = stripped_line[1:-1]
                        if not self.config_template.has_section(current_section):
                            self.config_template.add_section(current_section)
                        pending_comments = []
                        continue

                    if stripped_line.startswith('#') or stripped_line.startswith(';'):
                        comment_text = stripped_line.lstrip('#; ').strip()
                        pending_comments.append(comment_text)
                        continue
                    
                    match = re.match(r'^\s*([^=\s]+)\s*=\s*(.*)$', stripped_line)
                    if match and current_section:
                        key = match.group(1).strip()
                        value = match.group(2).strip()
                        
                        self.config_template.set(current_section, key, value)
                        
                        if pending_comments:
                            self.comments[(current_section, key)] = "\n".join(pending_comments)
                            pending_comments = []
                        continue
                    
                    pending_comments = []

        except FileNotFoundError:
            raise FileNotFoundError(f"'{self.org_config_file}' 檔案未找到。請確保它存在且包含預設配置。")
        except Exception as e:
            raise Exception(f"解析 '{self.org_config_file}' 失敗: {e}")

    def _load_config(self):
        try:
            self._parse_template_config()

            if os.path.exists(self.current_config_file):
                self.config_current.read(self.current_config_file, encoding='utf-8')
                self.statusBar.showMessage(f"已從 '{self.org_config_file}' 載入模板，並從 '{self.current_config_file}' 載入當前值。")
            else:
                self.statusBar.showMessage(f"已從 '{self.org_config_file}' 載入模板。'{self.current_config_file}' 不存在，將使用默認值。")
            
            self._clear_layout(self.config_layout)
            self.widgets.clear()

            for section in self.config_template.sections():
                group_box = QGroupBox(section)
                form_layout = QFormLayout()
                group_box.setLayout(form_layout)
                self.config_layout.addWidget(group_box)

                for key, default_value in self.config_template.items(section):
                    if self.config_current.has_option(section, key):
                        current_value = self.config_current.get(section, key)
                    else:
                        current_value = default_value

                    key_display_label = QLabel(key)
                    key_display_label.setFont(QFont("Arial", 10, QFont.Bold))

                    label_container = QWidget()
                    label_vbox = QVBoxLayout(label_container)
                    label_vbox.setContentsMargins(0, 0, 0, 0)

                    comment_text = self.comments.get((section, key), "")
                    
                    if comment_text:
                        comment_label = QLabel(comment_text)
                        comment_label.setStyleSheet("color: gray;")
                        comment_label.setWordWrap(True)
                        comment_label.setFont(QFont("Arial", 9))

                        label_vbox.addWidget(key_display_label)
                        label_vbox.addWidget(comment_label)
                        label_vbox.addStretch(1)
                    else:
                        label_vbox.addWidget(key_display_label)
                        label_vbox.addStretch(1)

                    if current_value.lower() in ['true', 'false']:
                        widget = QCheckBox()
                        if current_value.lower() == 'true':
                            widget.setChecked(True)
                        else:
                            widget.setChecked(False)
                    else:
                        widget = QLineEdit(current_value)
                    
                    form_layout.addRow(label_container, widget)
                    self.widgets[(section, key)] = widget

        except FileNotFoundError as e:
            QMessageBox.critical(self, "檔案未找到", str(e))
            self.statusBar.showMessage(f"載入失敗: {e}")
        except Exception as e:
            QMessageBox.critical(self, "載入錯誤", f"載入配置失敗: {e}")
            self.statusBar.showMessage(f"載入失敗。")

    def _save_config(self):
        new_config_to_save = configparser.ConfigParser()
        
        try:
            for (section, key), widget in self.widgets.items():
                if not new_config_to_save.has_section(section):
                    new_config_to_save.add_section(section)

                if isinstance(widget, QCheckBox):
                    new_config_to_save[section][key] = str(widget.isChecked()) 
                elif isinstance(widget, QLineEdit):
                    new_config_to_save[section][key] = widget.text()

            with open(self.current_config_file, 'w', encoding='utf-8') as configfile:
                new_config_to_save.write(configfile)
            
            self.statusBar.showMessage(f"成功儲存設定至 '{self.current_config_file}'。")
            QMessageBox.information(self, "儲存成功", f"設定已成功儲存至 '{self.current_config_file}'。")

        except Exception as e:
            QMessageBox.critical(self, "儲存錯誤", f"儲存配置失敗: {e}")
            self.statusBar.showMessage("儲存失敗。")

    def _clear_layout(self, layout):
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
                if item.layout():
                    self._clear_layout(item.layout())

def main():
    logger.info("main() 開始")

    config = load_config()
    if config is None:
        logger.critical("載入組態失敗。正在離開。")
        sys.exit(1)

    logger.info("開始獲取組態值")
    left_dataset_dir = config.get('LeftCamera', 'dataset_dir')
    right_dataset_dir = config.get('RightCamera', 'dataset_dir')
    left_cache_dir = config.get('LeftCamera', 'cache_dir')
    right_cache_dir = config.get('RightCamera', 'cache_dir')
    left_config_path = config.get('LeftCamera', 'roi_config_path')
    right_config_path = config.get('RightCamera', 'roi_config_path')

    left_image_save_dir = config.get('LeftCamera', 'image_save_dir')
    right_image_save_dir = config.get('RightCamera', 'image_save_dir')

    try:
        fixed_size = (config.getint('Detection', 'fixed_image_width'),
                      config.getint('Detection', 'fixed_image_height'))
    except ValueError:
        logger.error("fixed_image_width 或 fixed_image_height 不是有效的整數。使用預設值 (128, 128)。", exc_info=True)
        fixed_size = (128, 128)

    feature_config = config['Features']
    classifier_config = config['Classifier']

    logger.info("獲取組態值完成")
    try:
        os.makedirs(left_image_save_dir, exist_ok=True)
        os.makedirs(right_image_save_dir, exist_ok=True)
        os.makedirs(left_cache_dir, exist_ok=True)
        os.makedirs(right_cache_dir, exist_ok=True)
        logger.info(f"目錄已確保: {left_image_save_dir}, {right_image_save_dir}, {left_cache_dir}, {right_cache_dir}")
    except Exception as e:
        logger.critical(f"創建必要目錄失敗: {e}", exc_info=True)
        sys.exit(1)

    logger.info("創建必要目錄完成")
    logger.info("啟動程式 V2.1 (已修復並整合)...")

    left_rois = load_roi_from_config(left_config_path)
    right_rois = load_roi_from_config(right_config_path)
    if not left_rois:
        logger.warning(f"在 {left_config_path} 中未找到左側攝影機的 ROI 定義。左側攝影機的訓練/偵測可能無法進行。")
    if not right_rois:
        logger.warning(f"在 {right_config_path} 中未找到右側攝影機的 ROI 定義。右側攝影機的訓練/偵測可能無法進行。")

    logger.info("載入 ROI 完成")

    while True:
        print("\n=== 煙霧偵測系統 ===")
        print("1. 訓練左側分類器")
        print("2. 訓練右側分類器")
        print("3. 參數設定")
        print("4. 執行偵測系統")
        print("5. 離開")
        selection = input("請選擇: ").strip()

        if selection == "5":
            logger.info("使用者從選單退出程式。")
            break

        elif selection == "1":
            logger.info("正在訓練左側分類器...")
            try:
                left_X, left_y = load_cached_features(left_cache_dir)
                if len(left_X) == 0:
                    logger.info("未找到或左側快取為空，正在從資料集萃取特徵...")
                    left_X, left_y = load_dataset(left_dataset_dir, rois=left_rois, feature_config=feature_config, fixed_size=fixed_size)
                    if len(left_X) > 0:
                         cache_features(left_cache_dir, left_X, left_y)
                         logger.info(f"已從左側資料集萃取 {len(left_X)} 個樣本。")
                    else:
                         logger.error(f"未從位於 {left_dataset_dir} 的左側資料集萃取到樣本。無法訓練。")
                         print("未找到用於左側訓練的樣本。請檢查資料集。")
                         continue

                logger.info(f"正在使用 {len(left_X)} 個樣本進行左側訓練。")
                left_clf = train_classifier(left_X, left_y, classifier_config=classifier_config)
                if left_clf:
                    logger.info("左側分類器訓練成功完成。")
                    print("左側分類器已訓練。")
                else:
                    logger.error("左側分類器訓練失敗。")
                    print("左側分類器訓練失敗。")

            except Exception as e:
                logger.error(f"左側分類器訓練過程中發生錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"訓練期間發生錯誤: {e}")

        elif selection == "2":
            logger.info("正在訓練右側分類器...")
            try:
                right_X, right_y = load_cached_features(right_cache_dir)
                if len(right_X) == 0:
                    logger.info("未找到或右側快取為空，正在從資料集萃取特徵...")
                    right_X, right_y = load_dataset(right_dataset_dir, rois=right_rois, feature_config=feature_config, fixed_size=fixed_size)
                    if len(right_X) > 0:
                        cache_features(right_cache_dir, right_X, right_y)
                        logger.info(f"已從右側資料集萃取 {len(right_X)} 個樣本。")
                    else:
                        logger.error(f"未從位於 {right_dataset_dir} 的右側資料集萃取到樣本。無法訓練。")
                        print("未找到用於右側訓練的樣本。請檢查資料集。")
                        continue

                logger.info(f"正在使用 {len(right_X)} 個樣本進行右側訓練。")
                right_clf = train_classifier(right_X, right_y, classifier_config=classifier_config)
                if right_clf:
                    logger.info("右側分類器訓練成功完成。")
                    print("右側分類器已訓練。")
                else:
                    logger.error("右側分類器訓練失敗。")
                    print("右側分類器訓練失敗。")

            except Exception as e:
                logger.error(f"右側分類器訓練過程中發生錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"訓練期間發生錯誤: {e}")

        elif selection == "3":
            logger.info("正在啟動參數設定介面...")
            try:
                app_pyqt = QApplication.instance()
                if app_pyqt is None:
                    app_pyqt = QApplication(sys.argv)
                app_pyqt.setQuitOnLastWindowClosed(True)

                editor = ConfigEditorApp(org_config_file="config_org.ini", current_config_file="config.ini")
                editor.show()
                app_pyqt.exec_()
                
                logger.info("參數設定介面已關閉。")
                
                new_config = load_config()
                if new_config:
                    config = new_config
                    logger.info("Config 檔案已在參數編輯器關閉後重新載入。")
                else:
                    logger.error("參數編輯器關閉後重新載入 Config 失敗。將繼續使用之前的設定。")

            except Exception as e:
                logger.error(f"啟動參數設定介面錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"啟動參數設定介面錯誤: {e}")

        elif selection == "4":
            logger.info("正在啟動偵測模式...")
            try:
                current_left_clf = None
                left_X_ui, left_y_ui = load_cached_features(left_cache_dir)
                if len(left_X_ui) == 0:
                    logger.error(f"在快取 ({left_cache_dir}) 中未找到左側特徵。請先訓練左側分類器。")
                    print("未找到左側特徵。請先訓練左側分類器。")
                else:
                     logger.info("成功載入快取的左側特徵。正在為 UI 訓練左側分類器...")
                     current_left_clf = train_classifier(left_X_ui, left_y_ui, classifier_config=classifier_config)
                     if current_left_clf is None:
                         logger.error("從快取訓練左側分類器失敗。")
                         print("從快取特徵訓練左側分類器失敗。")

                current_right_clf = None
                right_X_ui, right_y_ui = load_cached_features(right_cache_dir)
                if len(right_X_ui) == 0:
                    logger.error(f"在快取 ({right_cache_dir}) 中未找到右側特徵。請先訓練右側分類器。")
                    print("未找到右側特徵。請先訓練右側分類器。")
                else:
                     logger.info("成功載入快取的右側特徵。正在為 UI 訓練右側分類器...")
                     current_right_clf = train_classifier(right_X_ui, right_y_ui, classifier_config=classifier_config)
                     if current_right_clf is None:
                         logger.error("從快取訓練右側分類器失敗。")
                         print("從快取特徵訓練右側分類器失敗。")

                if current_left_clf is None and current_right_clf is None:
                     logger.critical("兩個分類器都載入或訓練失敗。無法啟動偵測。")
                     print("兩個分類器都失敗。無法啟動偵測。")
                     continue

                logger.info("正在啟動 UI...")
                app = SmokeDetectionUI(config, current_left_clf, current_right_clf)
                app.mainloop()
                logger.info("UI 已關閉。")
                break

            except Exception as e:
                logger.error(f"偵測模式錯誤: {traceback.format_exc()}", exc_info=True)
                print(f"偵測模式錯誤: {e}")

        else:
            print("無效選擇，請重試")

    logger.info("程式正常結束。")
    logger.info("main() 結束")

if __name__ == "__main__":
    print("__main__ block started")
    print("Setting up initial logger formatter")
    initial_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    print("Initial logger formatter set")

    print("Attempting to add FileHandler")
    try:
        print("Inside FileHandler try block")
        initial_file_handler = logging.FileHandler("smoke_detection.log", mode='a', encoding='utf-8')
        initial_file_handler.setFormatter(initial_formatter)
        if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
            print("Adding FileHandler")
            logger.addHandler(initial_file_handler)
    except Exception as e:
        logger.error(f"無法設定初始檔案日誌: {e}", exc_info=True)
    print("FileHandler setup complete")

    print("Attempting to add StreamHandler")
    initial_stream_handler = logging.StreamHandler(sys.stdout)
    initial_stream_handler.setFormatter(initial_formatter)
    if not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        print("Adding StreamHandler")
        logger.addHandler(initial_stream_handler)
    print("StreamHandler setup complete")

    print("Setting default log level")
    logger.setLevel(logging.INFO)
    print("Default log level set")

    print("Calling main()")
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程式被使用者中斷 (KeyboardInterrupt)")
    except Exception as e:
        logger.critical(f"主執行中未處理的異常: {traceback.format_exc()}", exc_info=True)
        print(f"嚴重錯誤: {e}", file=sys.stderr)